import 'package:dio/dio.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';

class ProductSlugService {
  final Dio _dio = Dio();

  /// Get product reference from product slug
  Future<String?> getProductReferenceFromSlug({
    required String storeReference,
    required String productSlug,
  }) async {
    try {
      final url = '${AppConstants.baseUrl}/product/get_product_reference_from_slug/';
      
      final response = await _dio.get(
        url,
        queryParameters: {
          'store_reference': storeReference,
          'product_slug': productSlug,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
          },
        ),
      );

      if (response.statusCode == 200 && response.data['message'] == 'success') {
        return response.data['product_reference'];
      }
      
      return null;
    } catch (e) {
      // Handle error silently or log it
      return null;
    }
  }
}
