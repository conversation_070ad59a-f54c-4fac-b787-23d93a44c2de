import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/app.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:http/http.dart' as http;
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/buyers/messaging/buffer/message_repository.dart';
import 'dart:developer' as developer;
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';

class NewMessagingHomeScreen extends StatefulWidget {
  const NewMessagingHomeScreen({Key? key}) : super(key: key);

  // Public method to clear cache
  static final MessageRepository _messageRepository = MessageRepository();

  static Future<void> clearMessageCache(BuildContext context) async {
    await _messageRepository.clearCache();
  }

  @override
  State<NewMessagingHomeScreen> createState() => _NewMessagingHomeScreenState();
}

class _NewMessagingHomeScreenState extends State<NewMessagingHomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late WebSocketChannel? _channel;
  List<dynamic> _contacts = [];
  List<dynamic> _activeChats = [];
  List<dynamic> _requestChats = [];
  List<dynamic> _searchResults = [];
  bool _isLoading = true;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  final MessageRepository _messageRepository = MessageRepository();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _connectWebSocket();
    _loadChats();
    _loadContacts();
  }

  void _connectWebSocket() {
    developer.log('[ENTER] _connectWebSocket()',
        name: 'NewMessagingHomeScreen');

    try {
      final wsUrl = Uri.parse(
          '${AppConstants.newMessaging_webSocketConnection}${AppConstants.appData.newMessagingToken}');
      _channel = WebSocketChannel.connect(wsUrl);
      _channel?.stream.listen(
        (message) {
          try {
            developer.log('[ENTER] WebSocket onMessage()',
                name: 'NewMessagingHomeScreen');

            final data = jsonDecode(message);
            if (data['type'] == 'chat_update') {
              if (data['chats'] != null) {
                for (var chat in data['chats']) {
                  _messageRepository.updateChatInBuffer(chat);
                }
                _loadChats();
              }
            }

            developer.log('[EXIT] WebSocket onMessage(): type=${data['type']}',
                name: 'NewMessagingHomeScreen');
          } catch (e, stackTrace) {
            developer.log('[ERROR] WebSocket onMessage(): ${e.toString()}',
                name: 'NewMessagingHomeScreen', stackTrace: stackTrace);
            developer.log('[ERROR] Raw message that failed to parse: $message',
                name: 'NewMessagingHomeScreen');
          }
        },
        onError: (error) {
          developer.log('[ERROR] WebSocket onError(): ${error.toString()}',
              name: 'NewMessagingHomeScreen');
        },
        onDone: () {
          developer.log('[INFO] WebSocket connection closed',
              name: 'NewMessagingHomeScreen');
          Future.delayed(const Duration(seconds: 5), _connectWebSocket);
        },
      );

      developer.log('[EXIT] _connectWebSocket(): Connected successfully',
          name: 'NewMessagingHomeScreen');
    } catch (e, stackTrace) {
      developer.log('[ERROR] _connectWebSocket(): ${e.toString()}',
          name: 'NewMessagingHomeScreen', stackTrace: stackTrace);
      Future.delayed(const Duration(seconds: 5), _connectWebSocket);
    }
  }

  Future<void> _loadChats() async {
    try {
      developer.log('[ENTER] _loadChats(): Loading chats',
          name: 'NewMessagingHomeScreen');

      final response = await http.get(
        Uri.parse(AppConstants.newMessaging_getAllChats),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode == 200) {
        try {
          // First parse raw message with jsonDecode
          final data = jsonDecode(response.body);
          if (data is! List) {
            throw Exception(
                'Expected array of chats but got: ${data.runtimeType}');
          }

          developer.log('[INFO] _loadChats(): Retrieved ${data.length} chats',
              name: 'NewMessagingHomeScreen');

          final List<ChatInfo> chats = data.map((chatData) {
            return ChatInfo(
              chat_id: chatData['chat_id']?.toString() ?? '',
              chat_icon: chatData['chat_icon']?.toString(),
              chat_name: chatData['chat_name']?.toString() ?? '',
              chat_type: chatData['chat_type']?.toString() ?? 'DIRECT',
              user_id: chatData['user_id']?.toString() ?? '',
              is_subscribed: chatData['is_subscribed'] ?? false,
              unread_count:
                  int.tryParse(chatData['unread_count']?.toString() ?? '0') ??
                      0,
              last_accessed: chatData['last_accessed'] != null
                  ? DateTime.tryParse(chatData['last_accessed'].toString())
                  : null,
              last_read_sequence: int.tryParse(
                      chatData['last_read_sequence']?.toString() ?? '0') ??
                  0,
              entity_type: chatData['entity_type']?.toString() ?? 'USER',
              created_at:
                  DateTime.tryParse(chatData['created_at']?.toString() ?? '') ??
                      DateTime.now(),
              updated_at:
                  DateTime.tryParse(chatData['updated_at']?.toString() ?? '') ??
                      DateTime.now(),
              member_ids: chatData['member_ids'] != null
                  ? (chatData['member_ids'] as List)
                      .map((e) => e.toString())
                      .toList()
                  : null,
              chat_preview: chatData['chat_preview'] != null
                  ? ChatPreview.fromJson(chatData['chat_preview'])
                  : null,
              role_type: chatData['role_type']?.toString(),
              is_muted: chatData['is_muted'] ?? false,
              connecting_id: chatData['connecting_id']?.toString(),
              message_access: chatData['message_access']?.toString(),
            );
          }).toList();

          // Add all chats to buffer
          for (var chat in chats) {
            try {
              await _messageRepository.bufferManager.addChat(chat);
              developer.log('[INFO] _loadChats(): Added chat to buffer',
                  name: 'NewMessagingHomeScreen',
                  error: {
                    'chat_id': chat.chat_id,
                    'chat_name': chat.chat_name,
                  });
            } catch (e, stackTrace) {
              developer.log(
                  '[ERROR] _loadChats(): Failed to add chat to buffer',
                  name: 'NewMessagingHomeScreen',
                  error: {
                    'error': e.toString(),
                    'chat_id': chat.chat_id,
                  },
                  stackTrace: stackTrace);
            }
          }

          setState(() {
            _activeChats = chats.where((chat) => chat.is_subscribed).toList();
            _requestChats = chats.where((chat) => !chat.is_subscribed).toList();
            _isLoading = false;
          });

          developer.log(
              '[INFO] _loadChats(): Loaded ${_activeChats.length} active chats and ${_requestChats.length} request chats',
              name: 'NewMessagingHomeScreen');
        } catch (e, stackTrace) {
          developer.log(
              '[ERROR] _loadChats(): Error parsing chat data: ${e.toString()}',
              name: 'NewMessagingHomeScreen',
              stackTrace: stackTrace);
          developer.log(
              '[ERROR] Raw response that failed to parse: ${response.body}',
              name: 'NewMessagingHomeScreen');
          rethrow;
        }
      } else {
        throw Exception(
            'Failed to load chats: ${response.statusCode} - ${response.body}');
      }

      developer.log('[EXIT] _loadChats(): Chats loaded successfully',
          name: 'NewMessagingHomeScreen');
    } catch (e, stackTrace) {
      developer.log('[ERROR] _loadChats(): ${e.toString()}',
          name: 'NewMessagingHomeScreen', stackTrace: stackTrace);
      setState(() => _isLoading = false);
      rethrow;
    }
  }

  Future<void> _loadContacts() async {
    try {
      developer.log('[ENTER] _loadContacts(): Loading contacts',
          name: 'NewMessagingHomeScreen');

      final response = await http.get(
        Uri.parse(
            '${AppConstants.baseUrl}/user/get_chat_entity_recommendations/?limit=15&offset=0'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.accessToken}'
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        developer.log(
            '[INFO] _loadContacts(): Retrieved ${data.length} contacts',
            name: 'NewMessagingHomeScreen');

        setState(() {
          _contacts = data.map((contact) {
            // Debug print each contact
            // print('Processing contact: $contact');

            final Map<String, String> processedContact = {
              'chat_name': '',
              'chat_icon': '',
              'entity_type': '',
              'connecting_id': '',
              'user_reference': '',
            };

            try {
              // Process each field individually with detailed error logging
              processedContact['chat_name'] =
                  _processField(contact, 'chat_name', ['name']) ??
                      'Unknown Contact';
              processedContact['chat_icon'] =
                  _processField(contact, 'chat_icon', ['user_icon']) ?? '';
              processedContact['entity_type'] =
                  _processField(contact, 'entity_type', []) ?? 'USER';
              processedContact['connecting_id'] =
                  _processField(contact, 'connecting_id', ['user_reference']) ??
                      '';
              processedContact['user_reference'] =
                  _processField(contact, 'user_reference', []) ?? '';

              // print('Processed contact: $processedContact');
              return processedContact;
            } catch (e) {
              developer.log('[ERROR] Error processing contact: ${e.toString()}',
                  name: 'NewMessagingHomeScreen');
              return processedContact;
            }
          }).toList();
          _isLoading = false;
        });
      } else {
        developer.log('[ERROR] Failed to load contacts: ${response.statusCode}',
            name: 'NewMessagingHomeScreen');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to load contacts')),
          );
          setState(() => _isLoading = false);
        }
      }
    } catch (e) {
      developer.log('[ERROR] Error loading contacts: ${e.toString()}',
          name: 'NewMessagingHomeScreen');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading contacts: ${e.toString()}')),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  /// Helper method to safely process contact fields with fallbacks
  String? _processField(Map<dynamic, dynamic> contact, String primaryKey,
      List<String> fallbackKeys) {
    try {
      // Try primary key first
      if (contact.containsKey(primaryKey) && contact[primaryKey] != null) {
        final value = contact[primaryKey].toString().trim();
        // developer.log(
        //   '[INFO] Field $primaryKey = $value',
        //   name: 'NewMessagingHomeScreen'
        // );
        return value.isNotEmpty ? value : null;
      }

      // Try fallback keys
      for (final key in fallbackKeys) {
        if (contact.containsKey(key) && contact[key] != null) {
          final value = contact[key].toString().trim();
          // developer.log(
          //   '[INFO] Fallback field $key = $value',
          //   name: 'NewMessagingHomeScreen'
          // );
          return value.isNotEmpty ? value : null;
        }
      }

      // developer.log(
      //   '[INFO] No value found for $primaryKey or fallbacks $fallbackKeys',
      //   name: 'NewMessagingHomeScreen'
      // );
      return null;
    } catch (e) {
      // developer.log(
      //   '[ERROR] Error processing field $primaryKey: ${e.toString()}',
      //   name: 'NewMessagingHomeScreen'
      // );
      return null;
    }
  }

  void _openChat(dynamic chatOrContact) async {
    try {
      developer.log('[INFO] Opening chat with: $chatOrContact',
          name: 'NewMessagingHomeScreen');

      if (chatOrContact is ChatInfo) {
        developer.log('[INFO] Opening existing chat: ${chatOrContact.chat_id}',
            name: 'NewMessagingHomeScreen');
        // Use rootNavigator to push screen outside of PersistentTabView
        Navigator.of(context, rootNavigator: true)
            .push(
              MaterialPageRoute(
                builder: (context) => NewMessagingChatScreen(
                  chat_id: chatOrContact.chat_id,
                  chat_name: chatOrContact.chat_name,
                  chat_icon: chatOrContact.chat_icon?.toString() ?? '',
                  last_seen: chatOrContact.last_accessed != null
                      ? 'Last seen ${chatOrContact.last_accessed.toString()}'
                      : '',
                  connecting_id: '',
                  messageRepository: _messageRepository,
                  entity_type: chatOrContact.entity_type,
                ),
              ),
            )
            .then((_) => _loadChats());
      } else if (chatOrContact is Map) {
        developer.log('[INFO] Opening new chat from contact: $chatOrContact',
            name: 'NewMessagingHomeScreen');

        // Extract and validate required fields
        final Map<String, String> contact = Map<String, String>.from(
            chatOrContact.map((key, value) =>
                MapEntry(key.toString(), (value?.toString() ?? '').trim())));

        final String connectingId = contact['connecting_id'] ?? '';
        final String chatName = contact['chat_name'] ?? 'Unknown Contact';
        final String chatIcon = contact['chat_icon'] ?? '';
        final String entityType = contact['entity_type'] ?? 'USER';

        developer.log(
            '[INFO] Extracted contact data - connectingId: $connectingId, chatName: $chatName, chatIcon: $chatIcon',
            name: 'NewMessagingHomeScreen');

        if (connectingId.isEmpty) {
          developer.log(
              '[ERROR] Invalid contact data - missing connecting_id: $contact',
              name: 'NewMessagingHomeScreen');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Cannot open chat: Invalid contact ID')),
          );
          return;
        }

        // Use rootNavigator to push screen outside of PersistentTabView
        Navigator.of(context, rootNavigator: true)
            .push(
              MaterialPageRoute(
                builder: (context) => NewMessagingChatScreen(
                  chat_id: '',
                  chat_name: chatName,
                  chat_icon: chatIcon,
                  last_seen: '',
                  connecting_id: connectingId,
                  messageRepository: _messageRepository,
                  entity_type: entityType,
                ),
              ),
            )
            .then((_) => _loadChats());
      } else {
        developer.log(
            '[ERROR] Invalid chat or contact type: ${chatOrContact.runtimeType}',
            name: 'NewMessagingHomeScreen');
        throw Exception('Invalid chat or contact type');
      }
    } catch (e) {
      developer.log('[ERROR] Error opening chat: ${e.toString()}',
          name: 'NewMessagingHomeScreen');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to open chat: ${e.toString()}')),
      );
    }
  }

  Future<void> _handleRefresh() async {
    developer.log('[ENTER] _handleRefresh()', name: 'NewMessagingHomeScreen');

    try {
      setState(() => _isLoading = true);

      // Clear the cache first
      await _messageRepository.clearCache();

      // Reconnect websocket
      _channel?.sink.close();
      _connectWebSocket();

      // Reload all data
      await Future.wait([
        _loadChats(),
        _loadContacts(),
      ]);

      developer.log('[EXIT] _handleRefresh(): Refresh completed successfully',
          name: 'NewMessagingHomeScreen');
    } catch (e, stackTrace) {
      developer.log('[ERROR] _handleRefresh(): ${e.toString()}',
          name: 'NewMessagingHomeScreen', stackTrace: stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error refreshing: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Search for chats by name or username
  Future<void> _searchChats(String query) async {
    try {
      developer.log('[ENTER] _searchChats(): Searching for "$query"',
          name: 'NewMessagingHomeScreen');

      setState(() => _isLoading = true);

      // Construct the search URL with query, limit, and offset
      final searchUrl =
          '${AppConstants.newMessaging_baseUrl}/api/chats/search_chats/searchQuery=$query/limit=10/offset=0';

      developer.log('[INFO] _searchChats(): Making request to $searchUrl',
          name: 'NewMessagingHomeScreen');

      final response = await http.get(
        Uri.parse(searchUrl),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);

        developer.log('[INFO] _searchChats(): Found ${data.length} results',
            name: 'NewMessagingHomeScreen');

        if (mounted) {
          setState(() {
            _searchResults = data;
            _isSearching = true;
            _isLoading = false;
          });
        }
      } else {
        developer.log(
            '[ERROR] _searchChats(): API request failed with status ${response.statusCode}',
            name: 'NewMessagingHomeScreen',
            error: {'body': response.body});

        if (mounted) {
          setState(() {
            _searchResults = [];
            _isSearching = false;
            _isLoading = false;
          });
        }
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] _searchChats(): ${e.toString()}',
          name: 'NewMessagingHomeScreen', stackTrace: stackTrace);

      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
          _isLoading = false;
        });
      }
    }
  }

  /// Build the search results list
  Widget _buildSearchResults() {
    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final result = _searchResults[index];

        // Extract data from search result
        final String chatId = result['chat_id'] ?? '';
        final String chatName = result['chat_name'] ?? 'Unknown';
        final String chatIcon = result['chat_icon'] ?? '';
        final bool isSubscribed = result['is_subscribed'] ?? false;
        final String entityType = result['entity_type'] ?? 'USER';
        final String connectingId = result['connecting_id'] ?? '';

        // Create a chat object or contact map based on whether chat_id exists
        final chatOrContact = chatId.isNotEmpty
            ? ChatInfo(
                chat_id: chatId,
                chat_name: chatName,
                chat_icon: chatIcon,
                chat_type: 'DIRECT',
                user_id: result['user_id'] ?? '',
                is_subscribed: isSubscribed,
                unread_count: result['unread_count'] ?? 0,
                last_read_sequence: result['last_read_sequence'] ?? 0,
                entity_type: entityType,
                created_at: result['created_at'] != null
                    ? DateTime.parse(result['created_at'])
                    : DateTime.now(),
                updated_at: result['updated_at'] != null
                    ? DateTime.parse(result['updated_at'])
                    : DateTime.now(),
                role_type: result['role_type']?.toString(),
                is_muted: result['is_muted'] ?? false,
                connecting_id: connectingId,
              )
            : <String, String>{
                'chat_name': chatName,
                'chat_icon': chatIcon,
                'entity_type': entityType,
                'connecting_id': connectingId,
              };

        return ListTile(
          onTap: () => _openChat(chatOrContact),
          leading: CustomImageContainer(
            width: 40,
            height: 40,
            imageUrl: chatIcon.isNotEmpty ? chatIcon : null,
            imageType: entityType == 'STORE'
                ? CustomImageContainerType.store
                : CustomImageContainerType.user,
            // Show custom badge for GROUP chats, level badge for others
            showCustomBadge:
                chatOrContact is ChatInfo && chatOrContact.chat_type == 'GROUP',
            customBadgeSvgPath: AppImages.storeMessagingGroupLabel,
            showLevelBadge: !(chatOrContact is ChatInfo &&
                chatOrContact.chat_type == 'GROUP'),
            level: '1', // Default level for search results
          ),
          title: Text(
            chatName,
            style: AppTextStyle.access0(textColor: AppColors.writingBlack),
          ),
          subtitle: Text(
            result['chat_preview'] != null &&
                    result['chat_preview']['preview_text'] != null
                ? result['chat_preview']['preview_text']
                : chatId.isEmpty
                    ? 'Start a new conversation'
                    : 'No messages yet',
            style: AppTextStyle.subTitleRegular(
                textColor: AppColors.writingBlack1),
            maxLines: 1,
          ),
          trailing: chatId.isEmpty
              ? const Icon(Icons.message, color: AppColors.brandBlack)
              : null,
        );
      },
    );
  }

  /// Clears the chat cache and refreshes the UI
  Future<void> _clearCache() async {
    developer.log('[ENTER] _clearCache()', name: 'NewMessagingHomeScreen');

    try {
      setState(() => _isLoading = true);

      developer.log('Clearing chat cache', name: 'NewMessagingHomeScreen');

      await _messageRepository.clearCache();

      // Reload chats after clearing cache
      await _loadChats();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Cache cleared successfully')));
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] _clearCache(): ${e.toString()}',
          name: 'NewMessagingHomeScreen', stackTrace: stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error clearing cache: ${e.toString()}')));
      }
    } finally {
      setState(() => _isLoading = false);
      developer.log('[EXIT] _clearCache()', name: 'NewMessagingHomeScreen');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _channel?.sink.close();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Explicitly hide the bottom navigation bar
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          titleSpacing: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.appBlack),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            'Messaging',
            style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
          ),
          actions: [
            // Clear cache button
            IconButton(
              icon: const Icon(Icons.cleaning_services_rounded,
                  color: AppColors.appBlack),
              tooltip: 'Clear cache',
              onPressed: _clearCache,
            ),
          ],
          backgroundColor: AppColors.appWhite,
          elevation: 1,
          bottom: TabBar(
            controller: _tabController,
            labelColor: AppColors.appBlack,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.appBlack,
            tabs: [
              Tab(
                child: Text(
                  'Active',
                  style: AppTextStyle.settingHeading1(
                    textColor: _tabController.index == 0
                        ? AppColors.appBlack
                        : AppColors.writingBlack1,
                  ),
                ),
              ),
              Tab(
                child: Text(
                  'Requests',
                  style: AppTextStyle.settingHeading1(
                    textColor: _tabController.index == 1
                        ? AppColors.appBlack
                        : AppColors.writingBlack1,
                  ),
                ),
              ),
              Tab(
                child: Text(
                  'Contacts',
                  style: AppTextStyle.settingHeading1(
                    textColor: _tabController.index == 2
                        ? AppColors.appBlack
                        : AppColors.writingBlack1,
                  ),
                ),
              ),
            ],
          ),
        ),
        body: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              child: AppSearchField(
                textEditingController: _searchController,
                isActive: true,
                hintText: 'search stores and users',
                onChangeText: (value) {
                  if (value.isNotEmpty) {
                    _searchChats(value);
                  } else {
                    // When search text is cleared, reset search state and show original tabs
                    setState(() {
                      _isSearching = false;
                      _searchResults.clear();
                    });
                  }
                },
                onTapSuffix: () {
                  _searchController.clear();
                  setState(() {
                    _isSearching = false;
                    _searchResults.clear();
                  });
                },
                onSubmit: () {
                  if (_searchController.text.isNotEmpty) {
                    _searchChats(_searchController.text);
                  }
                },
              ),
            ),
            Expanded(
              child: _isSearching
                  ? _searchResults.isNotEmpty
                      ? _buildSearchResults()
                      : const Center(child: Text('No results found'))
                  : RefreshIndicator(
                      onRefresh: _handleRefresh,
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // Active Chats Tab
                          _isLoading
                              ? const Center(child: CircularProgressIndicator())
                              : _activeChats.isEmpty
                                  ? ListView(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      children: const [
                                        Center(
                                          child: Padding(
                                            padding: EdgeInsets.only(top: 100),
                                            child: Text('No active chats'),
                                          ),
                                        ),
                                      ],
                                    )
                                  : ListView.builder(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      itemCount: _activeChats.length,
                                      itemBuilder: (context, index) {
                                        try {
                                          final ChatInfo chat =
                                              _activeChats[index];
                                          return ListTile(
                                            onTap: () => _openChat(chat),
                                            leading: CustomImageContainer(
                                                width: 40,
                                                height: 40,
                                                imageUrl:
                                                    chat.chat_icon != null &&
                                                            chat.chat_icon!
                                                                .isNotEmpty
                                                        ? chat.chat_icon
                                                        : null,
                                                imageType: chat.entity_type ==
                                                        'STORE'
                                                    ? CustomImageContainerType
                                                        .store
                                                    : CustomImageContainerType
                                                        .user,
                                                // Show custom badge for GROUP chats, level badge for others
                                                showCustomBadge:
                                                    chat.chat_type == 'GROUP',
                                                customBadgeSvgPath: AppImages
                                                    .storeMessagingGroupLabel,
                                                badgeHeight: 20,
                                                badgeWidth: 20),
                                            title: Text(
                                              chat.chat_name,
                                              style: AppTextStyle.access0(
                                                  textColor:
                                                      AppColors.writingBlack),
                                            ),
                                            subtitle: Text(
                                              chat.chat_preview?.previewText ??
                                                  'No messages yet',
                                              style:
                                                  AppTextStyle.subTitleRegular(
                                                      textColor: AppColors
                                                          .writingBlack1),
                                              maxLines: 1,
                                            ),
                                            trailing: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  chat.last_accessed
                                                          ?.toString() ??
                                                      '',
                                                  style: const TextStyle(
                                                      color: Colors.grey,
                                                      fontSize: 12),
                                                ),
                                                if (chat.unread_count > 0)
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(6),
                                                    decoration:
                                                        const BoxDecoration(
                                                      color:
                                                          AppColors.brandBlack,
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Text(
                                                      '${chat.unread_count}',
                                                      style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 12),
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          );
                                        } catch (e, stackTrace) {
                                          developer.log(
                                              'Error rendering active chat item',
                                              name: 'NewMessagingHomeScreen',
                                              error: {
                                                'index': index,
                                                'error': e
                                              },
                                              stackTrace: stackTrace);
                                          return const ListTile(
                                            title:
                                                Text('Error displaying chat'),
                                            subtitle:
                                                Text('Please try refreshing'),
                                          );
                                        }
                                      },
                                    ),
                          // Requests Tab
                          _isLoading
                              ? const Center(child: CircularProgressIndicator())
                              : _requestChats.isEmpty
                                  ? ListView(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      children: const [
                                        Center(
                                          child: Padding(
                                            padding: EdgeInsets.only(top: 100),
                                            child: Text('No requested chats'),
                                          ),
                                        ),
                                      ],
                                    )
                                  : ListView.builder(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      itemCount: _requestChats.length,
                                      itemBuilder: (context, index) {
                                        final chat = _requestChats[index];
                                        return ListTile(
                                          onTap: () => _openChat(chat),
                                          leading: CustomImageContainer(
                                            width: 40,
                                            height: 40,
                                            imageUrl: chat.chat_icon != null &&
                                                    chat.chat_icon!.isNotEmpty
                                                ? chat.chat_icon
                                                : null,
                                            imageType: chat.entity_type ==
                                                    'STORE'
                                                ? CustomImageContainerType.store
                                                : CustomImageContainerType.user,
                                            // Show custom badge for GROUP chats, level badge for others
                                            showCustomBadge:
                                                chat.chat_type == 'GROUP',
                                            customBadgeSvgPath: AppImages
                                                .storeMessagingGroupLabel,
                                            showLevelBadge:
                                                chat.chat_type != 'GROUP',
                                            level:
                                                '1', // Default level for request chats
                                          ),
                                          title: Text(chat.chat_name),
                                          subtitle: Text(
                                            chat.chat_preview != null
                                                ? chat.chat_preview!.previewText
                                                : 'No messages yet',
                                            style: AppTextStyle.subTitleRegular(
                                                textColor:
                                                    AppColors.writingBlack1),
                                            maxLines: 1,
                                          ),
                                        );
                                      },
                                    ),
                          // Contacts Tab
                          _isLoading
                              ? const Center(child: CircularProgressIndicator())
                              : _contacts.isEmpty
                                  ? ListView(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      children: const [
                                        Center(
                                          child: Padding(
                                            padding: EdgeInsets.only(top: 100),
                                            child: Text('No contacts found'),
                                          ),
                                        ),
                                      ],
                                    )
                                  : ListView.builder(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      itemCount: _contacts.length,
                                      itemBuilder: (context, index) {
                                        final contact = _contacts[index];
                                        return ListTile(
                                          onTap: () => _openChat(contact),
                                          leading: CustomImageContainer(
                                            width: 40,
                                            height: 40,
                                            imageUrl:
                                                contact['chat_icon'] != null &&
                                                        contact['chat_icon']
                                                            .toString()
                                                            .isNotEmpty
                                                    ? contact['chat_icon']
                                                    : null,
                                            imageType: contact['entity_type'] ==
                                                    'STORE'
                                                ? CustomImageContainerType.store
                                                : CustomImageContainerType.user,
                                            // Contacts don't have chat_type, so only show level badge
                                            showCustomBadge: false,
                                            showLevelBadge: true,
                                            level:
                                                '1', // Default level for contacts
                                          ),
                                          title: Text(
                                            contact['chat_name'] ?? 'Unknown',
                                            style: AppTextStyle.access0(
                                                textColor:
                                                    AppColors.writingBlack),
                                          ),
                                          subtitle: Text(
                                            "send a message",
                                            style: AppTextStyle.subTitleRegular(
                                                textColor:
                                                    AppColors.writingBlack1),
                                          ),
                                          // trailing: ElevatedButton(
                                          //   onPressed: () => _openChat(contact),
                                          //   style: ElevatedButton.styleFrom(
                                          //     backgroundColor: AppColors.brandGreen,
                                          //     shape: RoundedRectangleBorder(
                                          //       borderRadius: BorderRadius.circular(20),
                                          //     ),
                                          //   ),
                                          //   child: const Text(
                                          //     'Message',
                                          //     style: TextStyle(color: Colors.white),
                                          //   ),
                                          // ),
                                        );
                                      },
                                    ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
