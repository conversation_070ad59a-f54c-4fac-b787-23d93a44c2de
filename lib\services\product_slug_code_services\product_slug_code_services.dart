import 'package:swadesic/model/product_slug_code_response/product_slug_code_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class ProductSlugCodeServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  ProductSlugCodeServices() {
    httpService = HttpService();
  }

  // endregion

  // region Check Product Slug Availability
  Future<ProductSlugCodeAvailabilityResponse> checkProductSlugAvailability({
    required String storeReference,
    required String searchQuery,
  }) async {
    Map<String, dynamic> response;
    var url = "${AppConstants.productSlugCodeAvailability}?store_reference=$storeReference&search_type=product_slug&search_query=$searchQuery";
    
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    return ProductSlugCodeAvailabilityResponse.fromJson(response);
  }
  // endregion

  // region Check Product Code Availability
  Future<ProductSlugCodeAvailabilityResponse> checkProductCodeAvailability({
    required String storeReference,
    required String searchQuery,
  }) async {
    Map<String, dynamic> response;
    var url = "${AppConstants.productSlugCodeAvailability}?store_reference=$storeReference&search_type=product_code&search_query=$searchQuery";
    
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    return ProductSlugCodeAvailabilityResponse.fromJson(response);
  }
  // endregion
}
