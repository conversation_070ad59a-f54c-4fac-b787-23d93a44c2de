import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class TypingSuggestionsOverlay extends StatelessWidget {
  final List<SuggestionItem> suggestions;
  final Function(SuggestionItem) onSuggestionTap;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final bool hasMore;

  const TypingSuggestionsOverlay({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.isLoading = false,
    this.onLoadMore,
    this.hasMore = false,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty && !isLoading) {
      return const SizedBox.shrink();
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        border: Border.all(color: AppColors.textFieldFill0),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppColors.appBlack.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: suggestions.length + (isLoading ? 1 : 0) + (hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < suggestions.length) {
            return _buildSuggestionItem(suggestions[index]);
          } else if (isLoading) {
            return _buildLoadingItem();
          } else if (hasMore) {
            return _buildLoadMoreItem();
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSuggestionItem(SuggestionItem suggestion) {
    return InkWell(
      onTap: () => onSuggestionTap(suggestion),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Profile image
            CustomImageContainer(
              width: 42,
              height: 42,
              imageUrl: suggestion.imageUrl,
              imageType: _getImageType(suggestion.type),
            ),
            const SizedBox(width: 12),
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (suggestion.secondaryText != null &&
                      suggestion.secondaryText!.isNotEmpty)
                    Text(
                      suggestion.secondaryText!,
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            // Type indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getTypeColor(suggestion.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _getTypeLabel(suggestion.type),
                style: AppTextStyle.contentText0(
                  textColor: _getTypeColor(suggestion.type),
                ).copyWith(fontSize: 10),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingItem() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildLoadMoreItem() {
    return InkWell(
      onTap: onLoadMore,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'Load more...',
            style: AppTextStyle.contentText0(
              textColor: AppColors.brandBlack,
            ),
          ),
        ),
      ),
    );
  }

  CustomImageContainerType _getImageType(String? type) {
    switch (type) {
      case 'USER':
        return CustomImageContainerType.user;
      case 'STORE':
        return CustomImageContainerType.store;
      case 'PRODUCT':
        return CustomImageContainerType.product;
      default:
        return CustomImageContainerType.user;
    }
  }

  Color _getTypeColor(String? type) {
    switch (type) {
      case 'USER':
        return AppColors.orange;
      case 'STORE':
        return AppColors.brandGreen;
      case 'PRODUCT':
        return AppColors.lightGreen;
      default:
        return AppColors.writingBlack1;
    }
  }

  String _getTypeLabel(String? type) {
    switch (type) {
      case 'USER':
        return 'User';
      case 'STORE':
        return 'Store';
      case 'PRODUCT':
        return 'Product';
      default:
        return '';
    }
  }
}
