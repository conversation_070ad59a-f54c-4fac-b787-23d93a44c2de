import 'package:badges/badges.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/banner/banner.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access.dart';
import 'package:swadesic/features/buyers/buyer_home/preview/preview.dart';
import 'package:swadesic/features/buyers/buyer_home/visited_store_grid/visited_store.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/messaging_and_requests_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/feed/feed_screen.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import 'package:badges/badges.dart' as badges;
import 'package:swadesic/features/buyers/messaging/new_messaging_home_screen.dart';

class BuyerHomeScreen extends StatefulWidget {
  final bool isFromOnboardingFlow;
  const BuyerHomeScreen({Key? key, this.isFromOnboardingFlow = false})
      : super(key: key);

  @override
  State<BuyerHomeScreen> createState() => _BuyerHomeScreenState();
}

class _BuyerHomeScreenState extends State<BuyerHomeScreen>
    with
        SingleTickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<BuyerHomeScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;
  // region Bloc
  late BuyerHomeBloc buyerHomeBloc;
  //Tab controller
  late TabController tabController =
      TabController(length: 2, vsync: this, initialIndex: 0);

// endregion

// region Init
  @override
  void initState() {
    //print("Init");
    buyerHomeBloc =
        BuyerHomeBloc(context, tabController, widget.isFromOnboardingFlow);
    buyerHomeBloc.init();
    AppConstants.isBottomNavigationMounted.value = true;
    super.initState();
  }

//region Dispose
  @override
  void dispose() {
    imageCache.clear();
    buyerHomeBloc.dispose();
    super.dispose();
  }

//endregion

// endregion

// region build
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<int>(
        stream: buyerHomeBloc.bottomCtrl.stream,
        initialData: 0,
        builder: (context, snapshot) {
          return WillPopScope(
            onWillPop: () async {
              return false;
            },
            child: Scaffold(
              appBar: appBar(),
              backgroundColor: AppColors.appWhite,
              body: SafeArea(child: body()),
            ),
          );
        });
  }

// endregion

  //region App bar
  AppBar appBar() {
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context);

    return AppCommonWidgets.mainAppBar(
        isCartVisible: false,
        leadingIcon: AppImages.locationIcon,
        isCustomLeadingIcon: true,
        onTapLeading: () async {
          buyerHomeBloc.pinCodeDialogBox();
        },
        context: context,
        isCenterTitle: true,
        isCustomTitle: true,
        customTitleWidget: const Preview(),
        customMenuButton: Row(
          children: [
            //Message

            Consumer<LoggedInUserInfoDataModel>(
              builder: (BuildContext context, LoggedInUserInfoDataModel value,
                  Widget? child) {
                // //print("User unread count is ${value.userDetail!.unreadConversationsCount!}");

                //If store view
                if (AppConstants.appData.isStoreView!) {
                  return const SizedBox();
                }

                //If value is null then return
                if (value.userDetail!.userReference == null) {
                  return Row(
                    children: [
                      // Container(
                      //   height: kToolbarHeight,
                      //   margin: value.userDetail!.unreadConversationsCount==0?EdgeInsets.zero:EdgeInsets.only(right: 5,top: 5),
                      //   child: GestureDetector(
                      //     onTap: () async{
                      //       //Else if static user then open login screen
                      //       if(CommonMethods().isStaticUser()){
                      //         CommonMethods().goToSignUpFlow();
                      //         return ;
                      //       }
                      //       ///Go to message screen
                      //       else{
                      //         buyerHomeBloc.goToMessagingScreen();
                      //       }
                      //     },
                      //     child: StreamBuilder<bool>(
                      //         stream: AppConstants.bottomNavigationRefreshCtrl.stream,
                      //         builder: (context, snapshot) {
                      //           return SvgPicture.asset(
                      //             AppImages.messageInAppBar,
                      //             height: kToolbarHeight - 30,
                      //             fit: BoxFit.contain,
                      //             color: AppColors.appBlack,
                      //           );
                      //         }),
                      //   ),
                      // ),
                      const SizedBox(width: 16),
                      Container(
                        height: kToolbarHeight,
                        margin: const EdgeInsets.only(right: 5, top: 5),
                        child: GestureDetector(
                          onTap: () {
                            if (CommonMethods().isStaticUser()) {
                              CommonMethods().goToSignUpFlow();
                              return;
                            }
                            // Use rootNavigator to push screen outside of PersistentTabView
                            Navigator.of(context, rootNavigator: true).push(
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const NewMessagingHomeScreen()),
                            );
                          },
                          child: SvgPicture.asset(
                            AppImages.messageInAppBar,
                            height: kToolbarHeight - 30,
                            fit: BoxFit.contain,
                            color: AppColors.appBlack,
                          ),
                        ),
                      ),
                    ],
                  );
                }

                return Row(
                  children: [
                    // badges.Badge(
                    //   showBadge: (value.userDetail!.unreadConversationsCount! + value.userDetail!.unreadConversationsCount!)== 0 ? false : true,
                    //   badgeStyle: const BadgeStyle(
                    //     padding: EdgeInsets.all(5),
                    //     badgeColor: AppColors.writingBlack1,
                    //   ),
                    //   badgeContent: Text(
                    //     "${(value.userDetail!.unreadConversationsCount!) == 0 ? "" : (value.userDetail!.unreadConversationsCount!) > 9 ? "+9" : (value.userDetail!.unreadConversationsCount!)}",
                    //     style: const TextStyle(color: AppColors.appWhite, fontSize: 10, fontWeight: FontWeight.w700),
                    //   ),
                    //   position: BadgePosition.custom(top: 10,isCenter: false,end: 0),
                    //   child: Container(
                    //     height: kToolbarHeight,
                    //     margin: value.userDetail!.unreadConversationsCount==0?EdgeInsets.zero:EdgeInsets.only(right: 5,top: 5),
                    //     child: GestureDetector(
                    //       onTap: () async{
                    //         if(CommonMethods().isStaticUser()){
                    //           CommonMethods().goToSignUpFlow();
                    //           return ;
                    //         }
                    //         buyerHomeBloc.goToMessagingScreen();
                    //       },
                    //       child: StreamBuilder<bool>(
                    //           stream: AppConstants.bottomNavigationRefreshCtrl.stream,
                    //           builder: (context, snapshot) {
                    //             return SvgPicture.asset(
                    //               AppImages.messageInAppBar,
                    //               height: kToolbarHeight - 30,
                    //               fit: BoxFit.contain,
                    //               color: AppColors.appBlack,
                    //             );
                    //           }),
                    //     ),
                    //   ),
                    // ),
                    const SizedBox(width: 16),
                    Container(
                      height: kToolbarHeight,
                      margin: const EdgeInsets.only(right: 5, top: 5),
                      child: GestureDetector(
                        onTap: () {
                          if (CommonMethods().isStaticUser()) {
                            CommonMethods().goToSignUpFlow();
                            return;
                          }
                          // Use rootNavigator to push screen outside of PersistentTabView
                          Navigator.of(context, rootNavigator: true).push(
                            MaterialPageRoute(
                                builder: (context) =>
                                    const NewMessagingHomeScreen()),
                          );
                        },
                        child: SvgPicture.asset(
                          AppImages.messageInAppBar,
                          height: kToolbarHeight - 30,
                          fit: BoxFit.contain,
                          color: AppColors.appBlack,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),

///////////////////////////////////////////////////////
            const SizedBox(width: 5),
            Container(
              alignment: Alignment.center,
              height: kToolbarHeight,
              child: GestureDetector(
                onTap: () async {
                  //If web view
                  // if(kIsWeb){
                  //   return  await CommonMethods.appDownloadDialog();
                  // }
                  //Else if static user then open login screen
                  if (CommonMethods().isStaticUser()) {
                    CommonMethods().goToSignUpFlow();
                    return;
                  }

                  ///If on tap cart is not empty then go to shopping cart else call the onTapCart method
                  else {
                    var screen = const ShoppingCartScreen();
                    var route = MaterialPageRoute(builder: (context) => screen);
                    Navigator.push(context, route).then((value) {
                      //Refresh cart
                      AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
                    });
                  }
                },
                child: StreamBuilder<bool>(
                    stream: AppConstants.bottomNavigationRefreshCtrl.stream,
                    builder: (context, snapshot) {
                      return badges.Badge(
                        showBadge: shoppingCartQuantityDataModel
                            .productReferenceList.isNotEmpty,
                        // padding: const EdgeInsets.all(5),
                        badgeStyle: const badges.BadgeStyle(
                            badgeColor: AppColors.brandBlack,
                            borderSide: BorderSide(
                              color: AppColors.appWhite,
                              width: 1.0,
                            )),
                        badgeContent: Text(
                          shoppingCartQuantityDataModel
                                      .productReferenceList.length >
                                  9
                              ? "9+"
                              : shoppingCartQuantityDataModel
                                  .productReferenceList.length
                                  .toString(),
                          style: const TextStyle(
                              color: AppColors.appWhite,
                              fontSize: 10,
                              fontWeight: FontWeight.w700),
                        ),
                        //alignment: Alignment.center,
                        position: BadgePosition.custom(
                            top: -4, end: 11, isCenter: false),
                        child: Container(
                          margin: EdgeInsets.only(
                              right: shoppingCartQuantityDataModel
                                      .productReferenceList.isEmpty
                                  ? 0
                                  : 5),
                          child: SvgPicture.asset(
                            AppImages.basket,
                            height: kToolbarHeight - 27,
                            fit: BoxFit.contain,
                            color: AppColors.appBlack,
                          ),
                        ),
                      );
                    }),
              ),
            ),
            const SizedBox(width: 10),
          ],
        ),
        isCustomMenuVisible: true,
        isDefaultMenuVisible: false);
  }
  //endregion

  //region Body
  Widget body() {
    return Column(
      children: [
        tabs(),
        divider(),
        Expanded(child: tabView()),
      ],
    );
  }
  //endregion

// region Buyer home
  Widget buyerHome() {
    final name = context.watch<UserCreatedStoresDataModel>().counter;
    // final name = context.select((UserCreatedStoresDataModel p) => p.counter);
    return StreamBuilder<BuyerHomeScreenState>(
        stream: buyerHomeBloc.buyerHomeCtrl.stream,
        initialData: BuyerHomeScreenState.Loading,
        builder: (context, snapshot) {
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await buyerHomeBloc.init();
            },
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  BannerScreen(isFromFeed: false),
                  const VisitedStores(),
                  supportedStore(),
                  const HomeAccess(showSupportScore: false),
                  verticalSizedBox(20)
                ],
              ),
            ),
          );
        });
  }

// endregion

//region Supported store
  Widget supportedStore() {
    // buyerHomeBloc.goToStoreYouFollow();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 30),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          buyerHomeBloc.goToStoreYouFollow();
        },
        child: Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.symmetric(horizontal: 20),
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            borderRadius: BorderRadius.circular(120),

            border: Border.all(color: AppColors.brandBlack, width: 1.5),
            // boxShadow: [
            //   BoxShadow(
            //     offset: const Offset(0, 1),
            //     blurRadius: 5,
            //     color: AppColors.appBlack.withOpacity(0.2),
            //   ),
            // ],
          ),
          child: Text(
            AppStrings.supportedStores,
            style: AppTextStyle.access0(textColor: AppColors.brandBlack),
          ),
        ),
      ),
    );
  }
//endregion

//region Tabs
  StreamBuilder<bool> tabs() {
    return StreamBuilder<bool>(
        stream: buyerHomeBloc.refreshTabCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: kToolbarHeight * 0.8,
            child: TabBar(
                controller: buyerHomeBloc.tabController,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: AppColors.appBlack,
                    width:
                        2.0, // Explicitly set width to make indicator visible
                  ),
                ),
                onTap: (index) {
                  // notificationBloc.tabRefreshCtrl.sink.add(true);
                },
                padding: EdgeInsets.zero,
                // isScrollable: true,
                tabs: [
                  //Feed
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.feed,
                          style: AppTextStyle.settingHeading1(
                              textColor: buyerHomeBloc.tabController.index == 0
                                  ? AppColors.appBlack
                                  : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Home
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.home,
                          style: AppTextStyle.settingHeading1(
                              textColor: buyerHomeBloc.tabController.index == 1
                                  ? AppColors.appBlack
                                  : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                ]),
          );
        });
  }

//endregion

//region Tab view
  TabBarView tabView() {
    return TabBarView(controller: buyerHomeBloc.tabController, children: [
      //Feed screen
      FeedScreen(userOrStoreFeedReference: AppConstants.appData.userReference!),
      buyerHome(),
    ]);
  }

//endregion
}
