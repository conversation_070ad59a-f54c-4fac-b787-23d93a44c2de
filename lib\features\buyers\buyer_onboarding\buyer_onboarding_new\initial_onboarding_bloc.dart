import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_details_response/city_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class InitialOnboardingBloc {
  // region Common Methods
  BuildContext context;
  String gender = "Female";
  final String userReference;
  late CityResponse cityResponse;
  bool isExistUser = true;
  final String? icon;

  ///User Details
  late UserDetailsServices userDetailsServices;

  late CacheStorageService cacheStorageService;

  // endregion

  //region Controller
  final genderCtrl = StreamController<String>.broadcast();
  final refreshCtrl = StreamController<bool>.broadcast();
  final loadingCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  TextEditingController nameTextCtrl = TextEditingController();
  TextEditingController referralCodeTextCtrl = TextEditingController();
  TextEditingController userNameTextCtrl = TextEditingController();
  TextEditingController cityTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  InitialOnboardingBloc(this.context, this.userReference, this.icon);

  // endregion

  // region Init
  void init() {
    cacheStorageService = CacheStorageService();
    userDetailsServices = UserDetailsServices();
    getCityApiCall();
    getReferralCode();
  }
  // endregion

  //region get referral code
  void getReferralCode() async {
    String referralCode = await AppDataService().getReferralCode();
    DateTime date = await AppDataService().getReferralCodeSavedDate();
    if (referralCode.isNotEmpty &&
        (DateTime.now().difference(date).inDays < 10)) {
      referralCodeTextCtrl.text = referralCode;
    }
  }
  //endregion

  //region On Select Gender
  void onSelectGender(String selectedGender) {
    gender = selectedGender;
    genderCtrl.sink.add(gender);
  }
  //endregion

  //region Get city api call
  getCityApiCall() async {
    try {
      cityResponse = await userDetailsServices.getCity();
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
  //endregion

  //region On tap city
  void onTapCity() async {
    List<String> dataList = [];
    for (var data in cityResponse.data!) {
      dataList.add(data.city!);
    }

    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.searchCity,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value == null) {
        return;
      }
      cityTextCtrl.text = value;
      refreshCtrl.sink.add(true);
    });
  }
  //endregion

  //region Validate Fields
  bool validateFields() {
    if (nameTextCtrl.text.isEmpty ||
        cityTextCtrl.text.isEmpty ||
        gender.isEmpty ||
        userNameTextCtrl.text.trim().isEmpty) {
      CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
      return false;
    }

    //Use user invalid
    if (userNameTextCtrl.text.trim().isNotEmpty && isExistUser) {
      CommonMethods.toastMessage(AppStrings.enterValidUserName, context);
      return false;
    }

    return true;
  }
  //endregion

  //region Save User Data
  Future<bool> saveUserData() async {
    try {
      Map<String, dynamic> userData = {
        "gender": gender,
        "user_location": cityTextCtrl.text,
        "first_name": nameTextCtrl.text,
        "display_name": nameTextCtrl.text,
        "user_name": userNameTextCtrl.text.trim(),
      };

      // Add referral code if provided
      if (referralCodeTextCtrl.text.trim().isNotEmpty) {
        userData["referral_code"] = referralCodeTextCtrl.text.trim();
      }

      // Call the API to update user details
      await userDetailsServices.editUserProfile(
        data: userData,
        userReference: userReference,
      );

      return true;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return false;
    }
  }
  //endregion

  //region Get User Data
  Map<String, dynamic> getUserData() {
    return {
      "user_reference": userReference,
      "gender": gender,
      "user_location": cityTextCtrl.text,
      "first_name": nameTextCtrl.text,
      "display_name": nameTextCtrl.text,
      "user_name": userNameTextCtrl.text.trim(),
      "referral_code": referralCodeTextCtrl.text.trim(),
    };
  }
  //endregion

  //region Dispose
  void dispose() {
    genderCtrl.close();
    refreshCtrl.close();
    loadingCtrl.close();
  }
  //endregion
}
