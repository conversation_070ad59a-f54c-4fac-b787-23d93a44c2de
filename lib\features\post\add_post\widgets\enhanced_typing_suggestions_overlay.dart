import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

enum OverlayState {
  suggestions,
  storeOptions,
  storeProducts,
}

class EnhancedTypingSuggestionsOverlay extends StatefulWidget {
  final List<SuggestionItem> suggestions;
  final Function(SuggestionItem) onSuggestionTap;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final Function(String)? onSearchChanged;
  final bool showSearchBar;

  const EnhancedTypingSuggestionsOverlay({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.isLoading = false,
    this.onLoadMore,
    this.hasMore = false,
    this.onSearchChanged,
    this.showSearchBar = false,
  });

  @override
  State<EnhancedTypingSuggestionsOverlay> createState() => _EnhancedTypingSuggestionsOverlayState();
}

class _EnhancedTypingSuggestionsOverlayState extends State<EnhancedTypingSuggestionsOverlay> {
  OverlayState currentState = OverlayState.suggestions;
  SuggestionItem? selectedStore;
  List<SuggestionItem> storeProducts = [];
  bool isLoadingProducts = false;
  final TypingSuggestionsService _service = TypingSuggestionsService();
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.suggestions.isEmpty && !widget.isLoading && currentState == OverlayState.suggestions) {
      return const SizedBox.shrink();
    }

    return Container(
      constraints: BoxConstraints(
        maxHeight: widget.showSearchBar ? MediaQuery.of(context).size.height * 0.7 : 200,
      ),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        border: Border.all(color: AppColors.textFieldFill0),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppColors.appBlack.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showSearchBar) _buildSearchBar(),
          if (currentState != OverlayState.suggestions) _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        onChanged: widget.onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search for users, stores, or products...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    String title;
    switch (currentState) {
      case OverlayState.storeOptions:
        title = 'Mention Store';
        break;
      case OverlayState.storeProducts:
        title = 'Select Product';
        break;
      default:
        title = '';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.textFieldFill0),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _goBack,
            icon: const Icon(Icons.arrow_back, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
                  .copyWith(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    switch (currentState) {
      case OverlayState.suggestions:
        return _buildSuggestionsList();
      case OverlayState.storeOptions:
        return _buildStoreOptions();
      case OverlayState.storeProducts:
        return _buildStoreProductsList();
    }
  }

  Widget _buildSuggestionsList() {
    if (widget.suggestions.isEmpty && !widget.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('No results found'),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.suggestions.length + (widget.isLoading ? 1 : 0) + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < widget.suggestions.length) {
          return _buildSuggestionItem(widget.suggestions[index]);
        } else if (widget.isLoading) {
          return _buildLoadingItem();
        } else if (widget.hasMore) {
          return _buildLoadMoreItem();
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSuggestionItem(SuggestionItem suggestion) {
    return InkWell(
      onTap: () => _handleSuggestionTap(suggestion),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Profile image
            CustomImageContainer(
              width: 42,
              height: 42,
              imageUrl: suggestion.imageUrl,
              imageType: _getImageType(suggestion.type),
            ),
            const SizedBox(width: 12),
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (suggestion.secondaryText != null &&
                      suggestion.secondaryText!.isNotEmpty)
                    Text(
                      suggestion.secondaryText!,
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            // Type indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getTypeColor(suggestion.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _getTypeLabel(suggestion.type),
                style: AppTextStyle.contentText0(
                  textColor: _getTypeColor(suggestion.type),
                ).copyWith(fontSize: 10),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingItem() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildLoadMoreItem() {
    return InkWell(
      onTap: widget.onLoadMore,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'Load more...',
            style: AppTextStyle.contentText0(
              textColor: AppColors.brandBlack,
            ),
          ),
        ),
      ),
    );
  }

  CustomImageContainerType _getImageType(String? type) {
    switch (type) {
      case 'USER':
        return CustomImageContainerType.user;
      case 'STORE':
        return CustomImageContainerType.store;
      case 'PRODUCT':
        return CustomImageContainerType.product;
      default:
        return CustomImageContainerType.user;
    }
  }

  Color _getTypeColor(String? type) {
    switch (type) {
      case 'USER':
        return AppColors.orange;
      case 'STORE':
        return AppColors.brandGreen;
      case 'PRODUCT':
        return AppColors.lightGreen;
      default:
        return AppColors.writingBlack1;
    }
  }

  String _getTypeLabel(String? type) {
    switch (type) {
      case 'USER':
        return 'User';
      case 'STORE':
        return 'Store';
      case 'PRODUCT':
        return 'Product';
      default:
        return '';
    }
  }

  void _handleSuggestionTap(SuggestionItem suggestion) {
    if (suggestion.type == 'STORE') {
      setState(() {
        selectedStore = suggestion;
        currentState = OverlayState.storeOptions;
      });
    } else {
      widget.onSuggestionTap(suggestion);
    }
  }

  Widget _buildStoreOptions() {
    if (selectedStore == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Store info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                CustomImageContainer(
                  width: 32,
                  height: 32,
                  imageUrl: selectedStore!.imageUrl,
                  imageType: CustomImageContainerType.store,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedStore!.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ).copyWith(fontSize: 14),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Options
          _buildStoreOption(
            'Mention Store Only',
            Icons.store,
            () {
              widget.onSuggestionTap(selectedStore!);
              _resetToSuggestions();
            },
          ),
          const SizedBox(height: 8),
          _buildStoreOption(
            'Mention Store Products',
            Icons.inventory,
            () {
              _showStoreProducts();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStoreOption(String title, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.textFieldFill0),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.brandBlack, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                    .copyWith(fontSize: 13),
              ),
            ),
            const Icon(Icons.arrow_forward_ios, color: AppColors.writingBlack1, size: 14),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreProductsList() {
    if (isLoadingProducts && storeProducts.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (storeProducts.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Text(
            'No products found',
            style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: storeProducts.length,
      itemBuilder: (context, index) {
        final product = storeProducts[index];
        return InkWell(
          onTap: () {
            widget.onSuggestionTap(product);
            _resetToSuggestions();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                CustomImageContainer(
                  width: 32,
                  height: 32,
                  imageUrl: product.imageUrl,
                  imageType: CustomImageContainerType.product,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.primaryText ?? '',
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack,
                        ).copyWith(fontSize: 14),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (product.secondaryText != null)
                        Text(
                          product.secondaryText!,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack1,
                          ).copyWith(fontSize: 12),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showStoreProducts() async {
    if (selectedStore == null) return;

    setState(() {
      currentState = OverlayState.storeProducts;
      isLoadingProducts = true;
      storeProducts.clear();
    });

    try {
      String query = "@${selectedStore!.primaryText}/";
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      final response = await _service.getTypingSuggestions(
        query: query,
        limit: 20,
        offset: 0,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      setState(() {
        storeProducts = response.results ?? [];
        isLoadingProducts = false;
      });
    } catch (e) {
      setState(() {
        isLoadingProducts = false;
      });
      if (mounted) {
        CommonMethods.toastMessage('Failed to load products', context);
      }
    }
  }

  void _goBack() {
    setState(() {
      if (currentState == OverlayState.storeOptions) {
        _resetToSuggestions();
      } else if (currentState == OverlayState.storeProducts) {
        currentState = OverlayState.storeOptions;
        storeProducts.clear();
      }
    });
  }

  void _resetToSuggestions() {
    setState(() {
      currentState = OverlayState.suggestions;
      selectedStore = null;
      storeProducts.clear();
    });
  }
}
