# new add post api (with tags)

## curl
```bash
curl --location 'http://192.168.1.8:8000/graphdb/create_post/' \
--header 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzc4MTU4NzQ1LCJpYXQiOjE3NDY2MjI3NDUsImp0aSI6IjVkMDI0NGVjM2MwNTQ5OTg4MWIwNzM0NDY5ODQ0OTFlIiwidXNlcl9pZCI6IlUxNzQ0NDUwMjczNjAwIn0.p2hTsDiOXihsYRfgHPJUDW14VyItt4NeQeWcqiV1fas' \
--header 'Cookie: sessionid=vsgwcb03jimn4pgoaz7h37dmmn7i74hs' \
--form 'post_text="Tag content post "' \
--form 'user_reference="U1744450273600"' \
--form 'post_images=@"/K:/images/Repto icon.jpg"' \
--form 'tagged_references_json="[
    {
        \"reference\": \"U1749293572687\",
        \"type\": \"USER\",
        \"order\": 1
    },
    {
        \"type\": \"PRODUCT\",
        \"order\": 2,
        \"reference\": \"P1748859602906626RGWO\"
    },
    {
        \"type\": \"STORE\",
        \"order\": 3,
        \"reference\": \"S1744452089786\"
    }
]"'
```
## response
```json
{
    "message": "success",
    "post": {
        "post_id": 106,
        "quote_parent_id": null,
        "post_reference": "PO202506111311544937",
        "post_text": "Tag content post ",
        "created_date": "2025-06-11T13:11:54.085513+05:30",
        "tagged_references_json": [
            {
                "reference": "U1749293572687",
                "type": "USER",
                "order": 1
            },
            {
                "type": "PRODUCT",
                "order": 2,
                "reference": "P1748859602906626RGWO"
            },
            {
                "type": "STORE",
                "order": 3,
                "reference": "S1744452089786"
            }
        ],
        "is_deleted": false,
        "like_count": 0,
        "comment_count": 0,
        "repost_count": 0,
        "repost_plus_count": 0,
        "save_count": 0,
        "share_count": 0,
        "analytics_view_count": 0,
        "user_reference": "U1744450273600",
        "store_reference": null
    }
}

```





# 📌 Post Tag Management API

**Endpoint:** `/manage_post_tags/`  
**Method:** `POST`  
**Description:** Manage tags associated with a post in both Django DB and Neo4j. Supports removing, reordering, and fully updating tags.

---

## 🔁 Common Request Headers

```http
Content-Type: application/json
Authorization: Bearer <your_token_here>  # If authentication is used
```

---

## ⚙️ Supported Operations

---

### 1. 🔻 Remove Tag

**Operation:** `remove_tag`  
**Description:** Removes a specific tag from a post and its relationship in Neo4j, then reorders the remaining tags.

#### ✅ Required Fields
- `post_reference`: `string`
- `operation`: `"remove_tag"`
- `tag_reference`: `string` (tag to be removed)

#### 📤 Sample Request

```bash
curl -X POST http://<your-domain>/manage_post_tags/ -H "Content-Type: application/json" -d '{
  "post_reference": "post_123",
  "operation": "remove_tag",
  "tag_reference": "user_456"
}'
```

#### 📥 Sample Success Response

```json
{
  "message": "Tag removed successfully",
  "removed_tag": {
    "reference": "user_456",
    "type": "USER",
    "order": 2
  },
  "updated_tags": [
    {
      "reference": "store_789",
      "type": "STORE",
      "order": 1
    },
    {
      "reference": "product_321",
      "type": "PRODUCT",
      "order": 2
    }
  ]
}
```

#### ❌ Error Responses

```json
{
  "error": "post_reference and operation are required"
}
```

```json
{
  "error": "Tag not found in post"
}
```

---

### 2. 🔀 Reorder Tags

**Operation:** `reorder_tags`  
**Description:** Reorders tags on a post according to a new order.

#### ✅ Required Fields
- `post_reference`: `string`
- `operation`: `"reorder_tags"`
- `new_order`: `list<string>` (new order of tag references)

#### 📤 Sample Request

```bash
curl -X POST http://<your-domain>/manage_post_tags/ -H "Content-Type: application/json" -d '{
  "post_reference": "post_123",
  "operation": "reorder_tags",
  "new_order": ["product_321", "user_456", "store_789"]
}'
```

#### 📥 Sample Success Response

```json
{
  "message": "Tags reordered successfully",
  "updated_tags": [
    {
      "reference": "product_321",
      "type": "PRODUCT",
      "order": 1
    },
    {
      "reference": "user_456",
      "type": "USER",
      "order": 2
    },
    {
      "reference": "store_789",
      "type": "STORE",
      "order": 3
    }
  ]
}
```

#### ❌ Error Responses

```json
{
  "error": "new_order must be a list of tag references"
}
```

```json
{
  "error": "Tag reference user_999 not found in current tags"
}
```

---

### 3. 🔄 Update All Tags

**Operation:** `update_tags`  
**Description:** Replaces all existing tags with new ones and updates Neo4j relationships accordingly.

#### ✅ Required Fields
- `post_reference`: `string`
- `operation`: `"update_tags"`
- `tagged_references_json`: `list<object>`  
  Each object should include:
  - `reference`: `string`
  - `type`: `"USER" | "STORE" | "PRODUCT"`
  - `order`: 1

#### 📤 Sample Request

```bash
curl -X POST http://<your-domain>/manage_post_tags/ -H "Content-Type: application/json" -d '{
  "post_reference": "post_123",
  "operation": "update_tags",
  "tagged_references_json": [
    {
      "reference": "product_789",
      "type": "PRODUCT"
      "order": 1
    },
    {
      "reference": "store_222",
      "type": "STORE"
      "order": 2
    }
  ]
}'
```

#### 📥 Sample Success Response

```json
{
  "message": "Tags updated successfully",
  "updated_tags": [
    {
      "reference": "product_789",
      "type": "PRODUCT",
      "order": 1
    },
    {
      "reference": "store_222",
      "type": "STORE",
      "order": 2
    }
  ]
}
```

#### ❌ Error Responses

```json
{
  "error": "tagged_references_json must be a list"
}
```

---

## 🧪 Example Post Object (Before and After)

### 🔎 Initial `tagged_references_json` (Before Operation)

```json
[
  { "reference": "user_456", "type": "USER", "order": 1 },
  { "reference": "store_789", "type": "STORE", "order": 2 },
  { "reference": "product_321", "type": "PRODUCT", "order": 3 }
]
```

### 🔁 Final `tagged_references_json` (After Operation)

*Will vary depending on the operation*

---

## 🧠 Notes

- Neo4j relationships are synced along with the Django model.
- Tag `type` must be one of: `"USER"`, `"STORE"`, `"PRODUCT"`.
- Operations are transactional — if anything fails, changes are rolled back.
- Proper error logging is performed for missing entities in Neo4j.

---
