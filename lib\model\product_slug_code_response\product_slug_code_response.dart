class ProductSlugCodeAvailabilityResponse {
  String? message;
  String? available;

  ProductSlugCodeAvailabilityResponse({this.message, this.available});

  ProductSlugCodeAvailabilityResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    available = json['available'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['available'] = available;
    return data;
  }
}
