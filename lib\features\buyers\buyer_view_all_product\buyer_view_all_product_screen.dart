import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_all_product_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../model/store_product_response/store_product_response.dart';


// region Buyer View All Product Screen
class BuyerViewAllProductScreen extends StatefulWidget {
  final String searchKeyword;
  final List<Product> productList;

  const BuyerViewAllProductScreen({Key? key, required this.searchKeyword, required this.productList}) : super(key: key);

  @override
  _BuyerViewAllProductScreenState createState() => _BuyerViewAllProductScreenState();
}
// endregion

class _BuyerViewAllProductScreenState extends State<BuyerViewAllProductScreen> {
  //Width
  double width = 0.0;
  // region Bloc
  late BuyerViewAllProductBloc buyerViewAllProductBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerViewAllProductBloc = BuyerViewAllProductBloc(context,widget.productList,widget.searchKeyword);
    buyerViewAllProductBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
     // appBar: appBar(),
      body: SafeArea(child: body()),
    );
  }

  // endregion




  // region Body
  Widget body() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        width = constraints.maxWidth;
        return Column(
          children: [
            searchBar(),
            searchResult(),
            Expanded(child: productList()),
          ],
        );
      },

    );
  }

// endregion

  //region Search Result
  Widget searchResult(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 10),
      child: Row(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                AppStrings.searchResults,
                style: const TextStyle(
                  fontFamily: "LatoRegular",
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: AppColors.appBlack,
                ),
              ),
              Text(
                widget.searchKeyword,
                style: const TextStyle(
                  fontFamily: "LatoBold",
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: AppColors.appBlack,
                ),
              ),
            ],
          ),
          horizontalSizedBox(10),

          ///Un-comment
          // CupertinoButton(
          //     padding: EdgeInsets.zero,
          //     onPressed: (){},
          //     child: SvgPicture.asset(AppImages.filter,color: AppColors.appBlack,fit: BoxFit.fill,)),
        ],
      ),
    );
  }
  //endregion


  //region Searchbar
  Widget searchBar() {
    return Hero(
      tag: "search",
      child:Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        child: InkWell(
          onTap: (){
            Navigator.pop(context);
          },
          child: AppSearchField(
            textEditingController:TextEditingController(text:widget.searchKeyword ),
            isActive: false,

            hintText: AppStrings.search,

            onChangeText: (value){
              // buyerSearchBloc.onChangeTextField();
            },
            onTapSuffix: (){

            },
            onSubmit: (){

            },

          ),
        ),
      ),
      // child: Padding(
      //   padding: EdgeInsets.only(left: 15,right: 15,top: 10),
      //   child: TextFormField(
      //     autofocus: false,
      //
      //
      //     controller: buyerViewAllProductBloc.searchTextEditingCtrl,
      //     onChanged: (value) {
      //      // buyerSearchBloc.onChangeTextField(value);
      //     },
      //     onTap: (){
      //       Navigator.pop(context);
      //     },
      //
      //
      //     maxLines: 1,
      //     //controller: addProductBloc.hashTagsTextCtrl,
      //     // readOnly: true,
      //
      //     style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
      //     decoration: InputDecoration(
      //       prefixIcon: Padding(
      //         padding: EdgeInsets.symmetric(horizontal: 11.73),
      //         child: SvgPicture.asset(
      //           AppImages.searchBarIcon,
      //           fit: BoxFit.contain,
      //           color: AppColors.appBlack7,
      //         ),
      //       ),
      //       filled: true,
      //
      //       // contentPadding: EdgeInsets.all(0),
      //
      //       fillColor: AppColors.lightestGrey,
      //
      //       isDense: true,
      //
      //       hintText: "search products, stores & your friends",
      //       hintStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: AppColors.appBlack.withOpacity(0.4)),
      //       focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(22), borderSide: BorderSide.none),
      //       enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(22), borderSide: BorderSide.none),
      //     ),
      //   ),
      // ),
    );
  }

  //endregion

//region Product List
  Widget productList(){
    return GridView.builder(
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: false,
        padding: const EdgeInsets.only(bottom: 20),
        // physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: widget.productList.length,
        // itemCount: 6,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.6),
          crossAxisCount: 2,
          mainAxisSpacing: 0,
          crossAxisSpacing: 0,
          mainAxisExtent: (width / 2) +
              CommonMethods.textHeight(
                  context: context,
                  textStyle: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)) +
              CommonMethods.textHeight(
                  context: context,
                  textStyle: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)) +
              CommonMethods.textHeight(
                  context: context,
                  textStyle: AppTextStyle.access0(textColor: AppColors.appBlack)) + 5,
        ),
        itemBuilder:(BuildContext,index){
         // return Container(color: Colors.green,);

          return InkWell(
            onTap: (){
              buyerViewAllProductBloc.goToViewProductScreen(index);
            },
            child:Opacity(
              opacity: widget.productList[index].inStock == 0?0.4:1.0,
              child: Container(
                  padding: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                    color: Colors.white,
                    border: Border.all(color: AppColors.lightGray2),
                    boxShadow: [
                      BoxShadow(
                        offset: const Offset(0, 1),
                        blurRadius: 4,
                        color: AppColors.appBlack.withOpacity(0.1),
                      ),
                    ],
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      ///Product card
                      AppCommonWidgets.productCardInGreed(
                        productImage: widget.productList[index].prodImages!.isEmpty?null:widget.productList[index].prodImages![0].productImage,
                        productBrand: widget.productList[index].brandName!,
                        productName: widget.productList[index].productName!,
                        sellingPrice: widget.productList[index].sellingPrice!.toString(),
                        mrp: widget.productList[index].mrpPrice!.toString(),
                        context: context, screenWidth: width,
                      ),


                      // ///Shadow
                      // widget.productList[index].inStock == 0
                      //     ? Container(
                      //   padding: EdgeInsets.zero,
                      //   decoration: BoxDecoration(
                      //     borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                      //     color: AppColors.appWhite.withOpacity(0.2),
                      //     border: Border.all(color: AppColors.lightGray2),
                      //     boxShadow: [
                      //       BoxShadow(
                      //         offset: const Offset(0, 1),
                      //         blurRadius: 5,
                      //         color: AppColors.appBlack.withOpacity(0.2),
                      //       ),
                      //     ],
                      //   ),
                      // )
                      //     : const SizedBox()
                     ///Todo un-comment
                      // Positioned(left: 5, top: 5, child: productRatings(index)),
                      // Positioned(top: 0, right: 0, child: save(index))
                    ],
                  )),
            ),
            // child: Container(
            //     padding: EdgeInsets.zero,
            //     decoration: BoxDecoration(
            //
            //       borderRadius: BorderRadius.only(topLeft: Radius.circular(10),
            //           topRight: Radius.circular(10)),
            //       color: Colors.white,
            //       border: Border.all(color: AppColors.lightGray2),
            //       boxShadow: [
            //         BoxShadow(
            //           offset: const Offset(0, 1),
            //           blurRadius: 5,
            //           color: AppColors.appBlack.withOpacity(0.2),
            //         ),
            //       ],
            //     ),
            //     child: Stack(
            //       alignment: Alignment.center,
            //       children: [
            //         Column(
            //           mainAxisAlignment: MainAxisAlignment.start,
            //           crossAxisAlignment: CrossAxisAlignment.start,
            //           mainAxisSize: MainAxisSize.min,
            //           children: [
            //             Expanded(
            //                 child: productImage(index)
            //             ),
            //             SizedBox(
            //               height: 70,
            //               child: Column(
            //                 mainAxisAlignment: MainAxisAlignment.center,
            //                 crossAxisAlignment: CrossAxisAlignment.start,
            //                 mainAxisSize: MainAxisSize.min,
            //                 children: [
            //                   verticalSizedBox(5),
            //                   Expanded(child: productName(index)),
            //                   productPrice(index),
            //                   verticalSizedBox(5)
            //                 ],
            //               ),
            //             )
            //
            //
            //           ],
            //         ),
            //         Positioned(
            //             left: 5,
            //             top: 5,
            //
            //             child: productRatings(index)),
            //         Positioned(
            //             top: 0,
            //             right: 0,
            //             child: save(index))
            //       ],
            //     )
            // ),
          );

        }
    );
  }
  //endregion


  //region Brand and product name
  Widget brandAndProductName(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Text(
        "${widget.productList[index].brandName.toString()} ${widget.productList[index].productName.toString()}",
        maxLines: 2,
        style: TextStyle(
          fontFamily: "LatoRegular",
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.appBlack.withOpacity(0.7),
        ),
      ),
    );
  }

  //endregion

  //region Image
  Widget productImage(int index){
    return ClipRRect(
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(10),topRight: Radius.circular(15)),
        child:
        Center(
            child:widget.productList[index].prodImages!.isEmpty? SvgPicture.network("https://fl-1.cdn.flockler.com/embed/no-image.svg", fit: BoxFit.cover,):

        // FadeInImage.assetNetwork(
        //   placeholder: AppImages.noImage,
        //   placeholderCacheHeight: 222,
        //   placeholderCacheWidth: 222,
        //   image:buyerViewStoreBloc.storeProductResponse.data![index].prodImages![0].productImage!,
        //   fit: BoxFit.cover, width: double.infinity, height: double.infinity,
        //   imageCacheHeight: 532,
        //   imageCacheWidth: 481,
        // )

        extendedImage(widget.productList[index].prodImages![0].productImage!, context,300,300)

          // CachedNetworkImage(
          //   imageUrl: "${buyerViewStoreBloc.storeProductResponse.data![index].prodImages?[0].productImage.toString()}",
          //   fit: BoxFit.cover,
          //   placeholder: (context, url) => Center(child:SvgPicture.asset(AppImages.noImage)),
          //   errorWidget: (context, url, error) => const Icon(Icons.error),
          //   maxWidthDiskCache:300 ,
          //   height: double.infinity,
          //   width: double.infinity,
          //   maxHeightDiskCache:300 ,
          //   memCacheHeight: 300,
          //   memCacheWidth: 300,
          //
          // )

          // CachedNetworkImage(
          //   imageUrl: "${buyerViewStoreBloc.storeProductResponse.data![index].prodImages?[0].productImage.toString()}",
          //   fit: BoxFit.cover,
          //   width: double.infinity,
          //   height: double.infinity,
          //   filterQuality: FilterQuality.low,
          //   placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
          //   errorWidget: (context, url, error) => const Icon(Icons.error),
          // ),

          // Image.network("${buyerViewStoreBloc.storeProductResponse.data![index].prodImages?[0].productImage.toString()}",fit: BoxFit.cover,
          // width: double.infinity,
          //   height: double.infinity,
          //   filterQuality: FilterQuality.low,
          //
          // )

        )
    );
  }
  //endregion

  //region Product Name
  Widget productName(int index){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Text(widget.productList[index].productName.toString(),
        maxLines: 2,
        style: TextStyle(
          fontFamily: "LatoRegular",
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.appBlack.withOpacity(0.7),
        ),),
    );
  }
  //endregion

  //region Product price
  Widget productPrice(int index){
    return Padding(
      padding:const EdgeInsets.symmetric(horizontal: 5),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("₹ ${widget.productList[index].sellingPrice.toString()}",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w400,
              color: AppColors.appBlack,
              fontFamily: "LatoRegular",
            ),

          ),
          horizontalSizedBox(10),
          Text("₹ ${widget.productList[index].mrpPrice.toString()}",
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.appBlack6,
              fontFamily: "LatoRegular",
              decoration: TextDecoration.lineThrough,
            ),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Product Rating
  Widget productRatings(int index){
    return widget.productList[index].rating==null?const SizedBox():Container(
      height: 20,
      width: 20,
      padding: const EdgeInsets.symmetric(horizontal: 1,vertical: 1),
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.appWhite,
      ),
      child: Center(child: Text(widget.productList[index].rating!.toString(),style: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w700,
        color: AppColors.yellow,
        fontFamily: "LatoBold",
      ),)),
    );
  }
  //endregion

  //region Save
  Widget save(int index){
   // return Container();
    return StreamBuilder<bool>(
        stream: buyerViewAllProductBloc.screenRefreshCtrl.stream,
        builder: (context, snapshot) {
          return InkWell(
            // padding: EdgeInsets.zero,
            onTap:(){
             // buyerViewAllProductBloc.saveUnSaveProduct(index);
              buyerViewAllProductBloc.saveUnSaveProduct(widget.productList[index].productReference!,widget.productList[index].saveStatus!,index);
            } ,

            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8,vertical: 5),
              decoration: const BoxDecoration(
                color: AppColors.appWhite,
                borderRadius: BorderRadius.only(topRight: Radius.circular(10),bottomLeft: Radius.circular(10)),

              ),
              child: SvgPicture.asset(widget.productList[index].saveStatus==true?AppImages.bookmarkActive1:AppImages.bookmarkInactive1,fit: BoxFit.fill,),
            ),
          );
        }
    );
  }
//endregion





//region Old
// //region Products
// Widget products(){
//     return GridView.builder(
//         itemCount: 20,
//
//         gridDelegate:SliverGridDelegateWithFixedCrossAxisCount(
//           childAspectRatio: MediaQuery.of(context).size.width /
//               (MediaQuery.of(context).size.height / 1.3),
//           crossAxisCount: 2,
//           mainAxisSpacing: 1,
//           crossAxisSpacing: 1
//
//         ),
//         itemBuilder:(BuildContext,index){
//
//
//
//       return InkWell(
//         onTap: (){
//           buyerViewAllProductBloc.goToBuyerViewProduct();
//         },
//         child: Container(
//           padding: EdgeInsets.zero,
//           decoration: BoxDecoration(
//
//             borderRadius: BorderRadius.only(topLeft: Radius.circular(10),topRight: Radius.circular(10)),
//             color: Colors.white,
//             border: Border.all(color: AppColors.lightGray2),
//             boxShadow: [
//               BoxShadow(
//                 offset: const Offset(0, 1),
//                 blurRadius: 5,
//                 color: AppColors.appBlack.withOpacity(0.2),
//               ),
//             ],
//           ),
//           child: Stack(
//             children: [
//               Column(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Expanded(
//                       child: productImage(index)),
//                   verticalSizedBox(5),
//                   productName(),
//                   productPrice(),
//                   verticalSizedBox(5)
//
//
//                 ],
//               ),
//               productRatings(),
//               Align(
//                   alignment: Alignment.topRight,
//                   child: save())
//             ],
//           )
//         ),
//       );
//
//     }
//     );
// }
// //endregion
//
// //region Image
// Widget productImage(int index){
//     return ClipRRect(
//        borderRadius: BorderRadius.only(topLeft: Radius.circular(10),topRight: Radius.circular(15)),
//         child:
//         Image.network("https://source.unsplash.com/random?sig=$index",fit: BoxFit.cover,width: double.infinity,
//           height: double.infinity,)
//     );
// }
// //endregion
//
// //region Product Name
// Widget productName(){
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 5),
//       child: Text("iPhonej\njbb",
//         maxLines: 3,
//         style: TextStyle(
//         fontFamily: "LatoRegular",
//         fontSize: 14,
//         fontWeight: FontWeight.w400,
//         color: AppColors.appBlack.withOpacity(0.7),
//       ),),
//     );
//   }
// //endregion
//
// //region Product price
// Widget productPrice(){
//     return Padding(
//       padding:EdgeInsets.symmetric(horizontal: 5),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Text("₹ 400",
//             style: TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.w400,
//               color: AppColors.appBlack,
//               fontFamily: "LatoRegular",
//             ),
//
//           ),
//           horizontalSizedBox(10),
//           Text("₹ 5000",
//             style: TextStyle(
//               fontSize: 12,
//               fontWeight: FontWeight.w400,
//               color: AppColors.appBlack6,
//               fontFamily: "LatoRegular",
//               decoration: TextDecoration.lineThrough,
//             ),
//
//
//           ),
//
//
//         ],
//       ),
//     );
// }
// //endregion
//
//
// //region Product Rating
// Widget productRatings(){
//     return Padding(
//       padding:  EdgeInsets.only(left: 5,top: 5),
//       child: Container(
//         height: 20,
//         width: 20,
//         padding: EdgeInsets.symmetric(horizontal: 1,vertical: 1),
//         decoration: const BoxDecoration(
//           shape: BoxShape.circle,
//           color: AppColors.white,
//
//         ),
//         child: Center(child: Text("4.4",style: TextStyle(
//           fontSize: 12,
//           fontWeight: FontWeight.w700,
//           color: AppColors.yellow,
//           fontFamily: "LatoBold",
//         ),)),
//       ),
//     );
// }
// //endregion
//
// //region Save
// Widget save(){
//     return Container(
//        padding: EdgeInsets.symmetric(horizontal: 8,vertical: 5),
//
//       decoration: BoxDecoration(
//         color: AppColors.white,
//         borderRadius: BorderRadius.only(topRight: Radius.circular(10),bottomLeft: Radius.circular(10)),
//
//       ),
//       child: SvgPicture.asset(AppImages.bookmarkInactive,fit: BoxFit.fill,),
//     );
// }
// //endregion

//endregion



}
