import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';

class OrderHistoryCommonWidgets {
  //region Sub-orders
  static subOrder({required SubOrder subOrder,required BuildContext context,required onTapCard}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 20),
          child: SizedB<PERSON>(
            height: 51,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    child: appText(subOrder.productName!,
                      maxLine: 3,
                      fontWeight: FontWeight.w600,
                      color: AppColors.writingBlack,
                      fontSize: 14,
                      fontFamily: AppConstants.rRegular,
                      height: 1.19
                    ),
                  ),
                ),
                InkWell(
                  onTap: (){
                    var screen = BuyerViewSingleProductScreen(productReference:subOrder.productReference,productVersion:subOrder.productVersion);
                    // var screen = const MyOrdersScreen();
                    var route = MaterialPageRoute(builder: (context) => screen);
                    Navigator.push(context, route);
                  },
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(6)),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 15,vertical: 8),
                      height: double.infinity,
                      width: 35,
                      decoration: const BoxDecoration(
                          color: AppColors.appWhite,
                          borderRadius: BorderRadius.all(Radius.circular(6))),
                      child: extendedImage(
                        subOrder.productImage!,
                        context,
                        100,
                        100,
                        cache: true,
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: (){
                    onTapCard();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 5),
                    height: 30,
                    width: 30,
                    child: RotatedBox(
                        quarterTurns: 3,
                        child: SvgPicture.asset(AppImages.downArrow2,color: AppColors.darkGray,)),
                  ),
                )
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: divider(),
        ),
      ],
    );
  }
//endregion
}
