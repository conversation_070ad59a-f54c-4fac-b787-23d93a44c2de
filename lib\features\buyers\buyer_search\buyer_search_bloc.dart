import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_filter_sort/search_filter/search_screen_filter.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_all_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/buyer_search_response/search_response.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart' as store_list_response;
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/filters/search_screen_filter_model/search_screen_filter_model.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_filter_bottom_sheet/app_filter_single_bottom_sheet.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum BuyerSearchState { Loading, Success, Failed, History, Initial }

class BuyerSearchBloc {
  // region Common Variables
  BuildContext context;
  static int searchScreenSelectedTab =0;

  //region View More Item Count Variables (All)
  int allViewMoreStoreItemCount = 3;
  int allViewMorePostItemCount = 3;
  int allViewMoreProductItemCount = 3;
  int allViewMorePeopleItemCount = 3;
  Timer? timer;
  //Focus node
  late FocusNode focusNode = FocusNode();

  String pinCodeUsedToSearch = "110068";

  late BuyerSearchServices buyerSearchServices;
   final  TabController tabController ;
   final  TabController initialTabCtrl;


  ///Filter model
  late SearchScreenFilterModel searchScreenFilterModel = SearchScreenFilterModel(AppConstants.appData.isStoreView!?"52045":BuyerHomeBloc.userDetailsResponse.userDetail!.pincode!);

  ///Search List Response
  // late SearchResponse searchResponse;

  SearchResponse searchResponse = SearchResponse();

  //Product list
  List<Product> filteredProductList = [];

  // String historyOrSearch = 'history';
  //String AppCsearchResponseonstants.buyerSearchFilter = 'all';

  // ///Store List Response
  // //late store_list_response.StoreListResponse storeListResponse;
  // late store_list_response.StoreListResponse storeListResponse;
  //
  // ///Product List Response
  // //late ProductListResponse productListResponse;
  // late StoreProductResponse storeProductResponse;

  //endregion

  //region View More Item Count Variables (History)
  int historyViewMoreStoreItemCount = 3;
  int historyViewMoreProductItemCount = 3;
  int historyViewMorePeopleItemCount = 3;

  //endregion

  // endregion

  //region Controller

  final screenRefreshCtrl = StreamController<bool>.broadcast();
  final buyerSearchCtrl = StreamController<BuyerSearchState>.broadcast();
  final searchProgressState = StreamController<bool>.broadcast();
  final onChangeOptionCtrl = StreamController<bool>.broadcast();

  //final onTapSearchCtrl = StreamController<bool>.broadcast();

  //region View More Option Controller (All)
  final allViewMoreStoreCtrl = StreamController<int>.broadcast();
  final allViewMorePostCtrl = StreamController<int>.broadcast();
  final allViewMoreProductsCtrl = StreamController<int>.broadcast();
  final allViewMorePeopleCtrl = StreamController<int>.broadcast();

  //endregion
  //region View More Option Controller (History)
  final historyViewMoreStoreCtrl = StreamController<int>.broadcast();
  final historyViewMoreProductsCtrl = StreamController<int>.broadcast();
  final historyViewMorePeopleCtrl = StreamController<int>.broadcast();

  //endregion

  //region Text Controller
  static final searchTextEditingCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  BuyerSearchBloc(this.context, this.tabController,this.initialTabCtrl);

  // endregion

  // region Init
  void init() async {


    ///Store List
    buyerSearchServices = BuyerSearchServices();
    //Clear search field
    searchTextEditingCtrl.clear();
    // getSearchResult();
    //onChangeTextField();
    tabController.addListener(onChangeTabController);
    // Initial state
    buyerSearchCtrl.sink.add(BuyerSearchState.Initial);

    //Focus node listener
    focusNode.addListener(() {

      //If keyboard is opened and text is smaller then 3
      if (searchTextEditingCtrl.text.trim().isEmpty && focusNode.hasFocus) {
        //print("Cleared");
        buyerSearchCtrl.sink.add(BuyerSearchState.History);
        return;
      }
      //If text field is empty and keyboard is cleaned
      if (searchTextEditingCtrl.text.trim().isEmpty && !focusNode.hasFocus) {
        //print("Cleared");
        buyerSearchCtrl.sink.add(BuyerSearchState.Initial);
        return;
      }

    });
  }
// endregion


  //region Listen on press physical button
  void listenOnPressPhysicalButton(){

  }
  //endregion


  //region Tab controller
  void onChangeTabController() {
    BuyerSearchBloc.searchScreenSelectedTab = tabController.index;

    onChangeOptionCtrl.sink.add(true);
    screenRefreshCtrl.sink.add(true);
    // Perform actions based on the selected tab index
    // For example, you can update the UI, fetch data, etc.
  }

  //endregion



  //region On search
  void onSearch() {

  }
  //endregion


//region On Text Change
  void onChangeTextField() {

    if (timer?.isActive ?? false) {
      timer!.cancel();
    }
    timer = Timer(const Duration(seconds: 1), () {


      if ((searchTextEditingCtrl.text == "" || searchTextEditingCtrl.text.length < 3) && focusNode.hasFocus) {
        //print("Cleared");
        buyerSearchCtrl.sink.add(BuyerSearchState.History);

        return;
      }
      getSearchResult(pinCode: pinCodeUsedToSearch);
      //onChangeOptionCtrl.sink.add("all");
      // historyOrSearch = 'search';
      onChangeOptionCtrl.sink.add(true);
      screenRefreshCtrl.sink.add(true);


    });




  }

//endregion

  //region On tap Filter
  void onTapFilter() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return AppFilterSingleBottomSheet(
          firstScreen: SearchScreenFilter(
            searchScreenFilterModel: searchScreenFilterModel,
          ),
          secondScreen: Container(
            color: Colors.pink,
          ),
        );
      },
    ).then((value) {
      if (value == null) {
        return;
      } else {
        searchScreenFilterModel = value;
        applyFilter();
      }
    });
  }

//endregion

  //region Check the pin code is changed or not

  //endregion

  //region Apply filter
  void applyFilter() async {
    // if all are disabled
    // if (searchScreenFilterModel.isAllDisabled()) {
    //   return;
    // }
    //Clear all

    //If pin is different
    if (pinCodeUsedToSearch != searchScreenFilterModel.deliveryPinCode) {
      return getSearchResult(pinCode: searchScreenFilterModel.deliveryPinCode);
    }

    //Deliverable
    deliverableTo();

    // remove duplicate
    filteredProductList = filteredProductList.toSet().toList();

    //finalFilteredFeedbackList.addAll(rootFeedbackList);

    //If Text field is not empty then apply search
    // if(searchFieldTextCtrl.text.isNotEmpty){
    //   onSearch();
    // }

    //Demo
    // searchedRootFeedbackList = searchedRootFeedbackList.where((element){
    //
    //   //Date created and up vote
    //   supportFilterModel.withAttachments.isSelected && element.feedbackFiles!.isNotEmpty?
    //
    // }).toList();

    //Sort
    // filterSortTransactionList.sort()
    // filterSortTransactionList.sort((a, b) => a.orderValue!.compareTo(b.orderValue!));
    //filterSortTransactionList.sort((b,a) => a.orderValue!.compareTo(b.orderValue!));
    // filterSortTransactionList.reversed;

    ///Item count check
    itemCountCheck();
    //refresh
    buyerSearchCtrl.sink.add(BuyerSearchState.Success);
  }

//endregion

  ///Deliverable To
  //region Deliverable To
  deliverableTo() {
    //If both are false
    if (searchScreenFilterModel.no.isSelected == false && searchScreenFilterModel.yes.isSelected == false) {
      filteredProductList.addAll(searchResponse.product!);
    }

    //If it is NO
    if (searchScreenFilterModel.no.isSelected) {
      //Clear Products
      filteredProductList.clear();

      filteredProductList = searchResponse.product!.where((element) => !element.deliverable!).toList();
    }
    //If it is Yes

    if (searchScreenFilterModel.yes.isSelected) {
      //Clear Products
      filteredProductList.clear();

      filteredProductList = searchResponse.product!.where((element) => element.deliverable!).toList();
    }
    //Item count check
    itemCountCheck();

    //print(filteredProductList.toList());
  }

  //endregion
//133001

  //region Get Search Result
  void getSearchResult({required String pinCode}) async {


    buyerSearchCtrl.sink.add(BuyerSearchState.Success);

    return;
    // allViewMoreStoreItemCount = 3;
    //  allViewMorePostItemCount = 3;
    //  allViewMoreProductItemCount = 3;
    //  allViewMorePeopleItemCount = 3;
    //
    // // Get reference to the PostDataModel
    // var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    // try {
    //
    //   late Timer progressTimer;
    //
    //   // Set a flag to indicate whether progress should be shown
    //   bool showProgress = false;
    //
    //   // Start the progress timer
    //   progressTimer = Timer(const Duration(milliseconds: 200), () {
    //     // If the API call is still in progress after 300 milliseconds, show progress
    //     if (!showProgress) {
    //       //Progress true
    //       searchProgressState.sink.add(true);
    //     }
    //   });
    //
    //   // buyerSearchStateCtrl.sink.add(BuyerSearchState.Loading);
    //   searchResponse = await buyerSearchServices.getSearchResult(searchTextEditingCtrl.text, pinCode);
    //
    //   //Add post in post list
    //   postDataModel.addPostIntoList(postList: searchResponse.post!);
    //
    //   // Stop the progress timer
    //   progressTimer.cancel();
    //   //Progress false
    //   searchProgressState.sink.add(false);
    //   //Update the pin code used to srearch
    //   pinCodeUsedToSearch = pinCode;
    //   //Clear filtered product list
    //   filteredProductList.clear();
    //   //Add product list in filtered product list
    //   filteredProductList.addAll(searchResponse.product!);
    //   //Add keyword to history
    //   buyerSearchServices.addKeyWordHistory(query: searchTextEditingCtrl.text);
    //   buyerSearchCtrl.sink.add(BuyerSearchState.Success);
    //   // historyOrSearch = 'search';
    //   // AppConstants.buyerSearchFilter = AppConstants.buyerSearchFilter;
    //   onChangeOptionCtrl.sink.add(true);
    //   screenRefreshCtrl.sink.add(true);
    //
    //   ///If field is empty then show the history data
    //   // if (searchTextEditingCtrl.text == "" || searchTextEditingCtrl.text.length < 3) {
    //   //   //print("Cleared");
    //   //   buyerSearchCtrl.sink.add(BuyerSearchState.History);
    //   //   return;
    //   // }
    //
    //   ///Item count check
    //   itemCountCheck();
    //
    //   ///Apply filter
    //   applyFilter();
    // } on ApiErrorResponseMessage {
    //   buyerSearchCtrl.sink.add(BuyerSearchState.Failed);
    //   searchProgressState.sink.add(false);
    //
    //   // CommonMethods.toastMessage(AppStrings.error, context);
    //   return;
    // } catch (error) {
    //   buyerSearchCtrl.sink.add(BuyerSearchState.Failed);
    //   searchProgressState.sink.add(false);
    //
    //   // CommonMethods.toastMessage(AppStrings.error, context);
    //   return;
    // }
  }

  //endregion

  //region Item count check
  void itemCountCheck() {
    ///Post
    if (searchResponse.post!.length <= 3) {
      allViewMorePostItemCount = searchResponse.post!.length;
      allViewMorePostCtrl.sink.add(allViewMorePostItemCount);
      // AppConstants.buyerSearchFilter = AppConstants.buyerSearchFilter;
      screenRefreshCtrl.sink.add(true);

      onChangeOptionCtrl.sink.add(true);
    } else {
      allViewMorePostItemCount = 3;
      allViewMorePostCtrl.sink.add(allViewMorePostItemCount);
    }
    ///Store
    if (searchResponse.store!.length <= 3) {
      allViewMoreStoreItemCount = searchResponse.store!.length;
      allViewMoreStoreCtrl.sink.add(allViewMoreStoreItemCount);
      // AppConstants.buyerSearchFilter = AppConstants.buyerSearchFilter;
      screenRefreshCtrl.sink.add(true);

      onChangeOptionCtrl.sink.add(true);
    } else {
      allViewMoreStoreItemCount = 3;
      allViewMoreStoreCtrl.sink.add(allViewMoreStoreItemCount);
    }

    ///Product
    if (filteredProductList.length <= 3) {
      allViewMoreProductItemCount = filteredProductList.length;
      allViewMoreProductsCtrl.sink.add(allViewMoreProductItemCount);
      // AppConstants.buyerSearchFilter = AppConstants.buyerSearchFilter;
      screenRefreshCtrl.sink.add(true);

      onChangeOptionCtrl.sink.add(true);
    } else {
      allViewMoreProductItemCount = 3;
      allViewMoreProductsCtrl.sink.add(allViewMoreProductItemCount);
    }

    ///People
    if (searchResponse.user!.length <= 3) {
      allViewMorePeopleItemCount = searchResponse.user!.length;
      allViewMorePeopleCtrl.sink.add(allViewMorePeopleItemCount);
      // AppConstants.buyerSearchFilter = AppConstants.buyerSearchFilter;
      screenRefreshCtrl.sink.add(true);
      onChangeOptionCtrl.sink.add(true);
    } else {
      allViewMorePeopleItemCount = 3;
      allViewMoreProductsCtrl.sink.add(allViewMoreProductItemCount);
    }
  }

  //endregion

  //region Add search item api call
  addSearchedItemApi({required String searchedItem, required String searchType}) async {
    try {
      buyerSearchServices.addSearchedItem(
        searchedText: searchTextEditingCtrl.text,
        searchedItem: searchedItem,
        searchType: searchType,
      );
    } on ApiErrorResponseMessage {
      buyerSearchCtrl.sink.add(BuyerSearchState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      buyerSearchCtrl.sink.add(BuyerSearchState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

  //endregion

//region Un Used
  //region Get Store List
  // getStoreList() async {
  //   try {
  //     // buyerSearchStateCtrl.sink.add(BuyerSearchState.Loading);
  //     storeListResponse = await buyerSearchServices.getStoreList();
  //     //print(storeListResponse.data!.first.location);
  //     buyerSearchStateCtrl.sink.add(BuyerSearchState.Success);
  //   }
  //    on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     return;
  //     buyerSearchStateCtrl.sink.add(BuyerSearchState.Failed);
  //     var snackBar = SnackBar(content: Text(error.message.toString()));
  //     ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  //   catch (error) {
  //     buyerSearchStateCtrl.sink.add(BuyerSearchState.Failed);
  //     var snackBar = SnackBar(content: Text(error.toString()));
  //     ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  // }

  //endregion

  //region Get Product List
  // getProductList() async {
  //   try {
  //     buyerSearchStateCtrl.sink.add(BuyerSearchState.Loading);
  //     storeProductResponse = await buyerSearchServices.getProductList();
  //     // buyerSearchStateCtrl.sink.add(BuyerSearchState.Success);
  //   }
  //    on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     return;
  //     buyerSearchStateCtrl.sink.add(BuyerSearchState.Failed);
  //     var snackBar = SnackBar(content: Text(error.message.toString()));
  //     ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  //   catch (error) {
  //     buyerSearchStateCtrl.sink.add(BuyerSearchState.Failed);
  //     var snackBar = SnackBar(content: Text(error.toString()));
  //     ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  // }

  //endregion
//endregion

//region On Option Change
  void onOptionChange({required int index}) {
    //print(index);
    // historyOrSearch = "search";
    // AppConstants.buyerSearchFilter = option;
    tabController.index = index;
    BuyerSearchBloc.searchScreenSelectedTab = index;
    screenRefreshCtrl.sink.add(true);
    // onChangeOptionCtrl.sink.add(true);
  }

//endregion

//region View More (All)
//region All View More Store
  void allViewMoreStore(int data) {
    allViewMoreStoreItemCount = allViewMoreStoreItemCount + data;
    allViewMoreStoreCtrl.sink.add(allViewMoreStoreItemCount);

    ///Check item count is bigger then Api Store List
    if (allViewMoreStoreItemCount > searchResponse.store!.length) {
      CommonMethods.toastMessage(AppStrings.noMoreData, context);
      allViewMoreStoreItemCount = searchResponse.store!.length;
      allViewMoreStoreCtrl.sink.add(searchResponse.store!.length);
      screenRefreshCtrl.sink.add(true);
      return;
    } else {
      //print(allViewMoreStoreItemCount);
      allViewMoreStoreCtrl.sink.add(allViewMoreStoreItemCount);
    }
  }
//endregion

  //region All View More Post
  void allViewMorePost(int data) {
    allViewMorePostItemCount = allViewMorePostItemCount + data;
    allViewMorePostCtrl.sink.add(allViewMorePostItemCount);

    ///Check item count is bigger then Api Store List
    if (allViewMorePostItemCount > searchResponse.post!.length) {
      CommonMethods.toastMessage(AppStrings.noMoreData, context);
      allViewMorePostItemCount = searchResponse.post!.length;
      allViewMorePostCtrl.sink.add(searchResponse.post!.length);
      screenRefreshCtrl.sink.add(true);
      return;
    } else {
      //print(allViewMoreStoreItemCount);
      allViewMorePostCtrl.sink.add(allViewMorePostItemCount);
    }
  }
//endregion

//region All View More Product
  void allViewMoreProduct(int data) {
    allViewMoreProductItemCount = allViewMoreProductItemCount + data;

    ///Check item count is bigger then Api Store List
    if (allViewMoreProductItemCount > searchResponse.product!.length) {
      CommonMethods.toastMessage(AppStrings.noMoreData, context);
      allViewMoreProductItemCount = searchResponse.product!.length;
      allViewMoreProductsCtrl.sink.add(allViewMoreProductItemCount);
      return;
    } else {
      //print(allViewMoreProductItemCount);
      allViewMoreProductsCtrl.sink.add(allViewMoreProductItemCount);
    }
  }

//endregion

//region All View More People
  void allViewMorePeople(int data) {

    allViewMorePeopleItemCount = allViewMorePeopleItemCount + data;

    ///Check item count is bigger then Api Store List
    if (allViewMorePeopleItemCount > searchResponse.user!.length) {
      CommonMethods.toastMessage(AppStrings.noMoreData, context);
      allViewMorePeopleItemCount = searchResponse.user!.length;
      allViewMorePeopleCtrl.sink.add(allViewMorePeopleItemCount);
      return;
    } else {
      //print(allViewMorePeopleItemCount);
      allViewMorePeopleCtrl.sink.add(allViewMorePeopleItemCount);
    }



    //
    // allViewMorePeopleItemCount = allViewMorePeopleItemCount + data;
    // //print(allViewMorePeopleItemCount);
    // allViewMorePeopleCtrl.sink.add(allViewMorePeopleItemCount);
  }

//endregion
//endregion

//region View More (History)
//region History View More Store
  void historyViewMoreStore(int data) {
    historyViewMoreStoreItemCount = historyViewMoreStoreItemCount + data;
    //print(historyViewMoreStoreItemCount);
    historyViewMoreStoreCtrl.sink.add(historyViewMoreStoreItemCount);
  }

//endregion

//region All View More Product
  void historyViewMoreProduct(int data) {
    historyViewMoreProductItemCount = historyViewMoreProductItemCount + data;
    //print(historyViewMoreProductItemCount);
    historyViewMoreProductsCtrl.sink.add(historyViewMoreProductItemCount);
  }

//endregion

//region All View More People
  void historyViewMorePeople(int data) {
    historyViewMorePeopleItemCount = historyViewMorePeopleItemCount + data;
    //print(historyViewMorePeopleItemCount);
    historyViewMorePeopleCtrl.sink.add(historyViewMorePeopleItemCount);
  }

//endregion
//endregion

//region Go to View Product Screen
  void goToViewProductScreen(int index) {
    ///Add searched item
    addSearchedItemApi(searchedItem: searchResponse.product![index].productReference!, searchType: "PRODUCT");
    var screen = BuyerViewProductScreen(
      openingFrom: SearchScreenEnum.SEARCH_HISTORY,
      productList: searchResponse.product!,
      index: index,
      searchedText: searchTextEditingCtrl.text,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region Go to View All Product
  goToViewAllProducts() {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewProduct! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }
    var screen = BuyerViewAllProductScreen(
      searchKeyword: searchTextEditingCtrl.text,
      productList: filteredProductList,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

//region Go to View Store Screen
  goToViewStoreScreen(StoreInfo selectedStore) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewStores! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }

    ///Add searched item
    addSearchedItemApi(searchedItem:selectedStore.storeReference!, searchType: "STORE");
    var screen = BuyerViewStoreScreen(storeReference: selectedStore.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

  //region Go to Profile screen
  goToUserProfileScreen({required String userReference}) {
    ///Add searched item
    addSearchedItemApi(searchedItem: userReference, searchType: "USER");
    var screen = UserProfileScreen(
      userReference: userReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion
////////////////////Post


  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail})async{
    List<Map<String, dynamic>> accessOptions = [];
    if(postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ?AppConstants.appData.userReference!
            :AppConstants.appData.storeReference!))
    {
      accessOptions =

      [
        //Copy
        {
          'title': AppStrings.copyPostLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(context, AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.editPost.toLowerCase(),
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);


          },
        },
        //Delete post
        {
          'title': AppStrings.deletePost,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);

          },
        },
        // Add more options if needed
      ];
    }
    else{
      accessOptions = [
        {
          'title': AppStrings.reportThePost,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isProduct: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion

          },
        },
      ];

    }

    CommonMethods.accessBottomSheet(screen: ShareAccessBottomSheet(accessOptions: accessOptions), context: context,);

  }
  //endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}){
    return CommonMethods.appDialogBox(
        context: context,
        widget:
        OkayAndCancelDialogScreen(onTapSecondButton:(){
          deletePost(postDetail: postDetail);
        },previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",

        )
    );
  }
//endregion


  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty ? null : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,

      ), context: context,);
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}){
    var screen = SinglePostViewScreen(postReference: postReference,);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}){
    Widget screen= EditPostScreen(postDetail: postDetail,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  //region Delete post api call
  Future<void> deletePost({required PostDetail postDetail}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //region Try
    try {
      //Api call
      UploadFileService().deletePost(postReference: postDetail.postOrCommentReference!);
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region On tap heart
  Future<void>onTapHeart({required PostDetail postDetail})async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      postDetail.likeStatus = await PostService().likePost(postReference: postDetail.postOrCommentReference!, likeStatus: !postDetail.likeStatus!);

      //Update liked count and is liked
      if(postDetail.likeStatus!){
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      }
      else{
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion

  //region On tap image
  void onTapImage({required List<String> imageList, required int index}) {
    Widget screen = BuyerImagePreviewScreen(
      productImage: imageList,
      imageIndex: index,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

  //endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference && AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference && AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion


//region Dispose
  void dispose() {
    imageCache.clear();
    buyerSearchCtrl.close();
    onChangeOptionCtrl.close();
    allViewMoreStoreCtrl.close();
    allViewMoreProductsCtrl.close();
    allViewMorePeopleCtrl.close();
    historyViewMoreStoreCtrl.close();
    historyViewMoreProductsCtrl.close();
    historyViewMorePeopleCtrl.close();
    searchProgressState.close();
    timer?.cancel();
  }
//endregion
}
