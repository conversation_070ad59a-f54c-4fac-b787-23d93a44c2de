import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/download_app_banner/download_app_banner_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class DownloadAppBanner extends StatefulWidget {
  const DownloadAppBanner({super.key});

  @override
  State<DownloadAppBanner> createState() => _DownloadAppBannerState();
}

class _DownloadAppBannerState extends State<DownloadAppBanner> {
  //region Bloc
  late DownloadAppBannerBloc downloadAppBannerBloc;
  //endregion

  //region Init
  @override
  void initState() {
    downloadAppBannerBloc = DownloadAppBannerBloc(context);
    downloadAppBannerBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Material(color: Colors.transparent, child: body());
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: downloadAppBannerBloc.isHideCtrl.stream,
        initialData: kIsWeb ? false : true,
        builder: (context, snapshot) {
          // if false then show
          if (!snapshot.data!) {
            return Container(
              margin: const EdgeInsets.all(7),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(17),
                color: AppColors.appWhite,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.appBlack.withOpacity(0.5),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // App Icon
                  Image.asset(
                    AppImages.appIcon,
                    height: 35,
                    width: 35,
                  ),
                  const SizedBox(width: 12),

                  // Text
                  Expanded(
                    child: Text(
                      "Use Swadesic with no limits",
                      style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Get the app button
                  CupertinoButton(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    color: AppColors.brandBlack,
                    borderRadius: BorderRadius.circular(80),
                    onPressed: () {
                      CommonMethods.openUrl(url: AppConstants.appPlayStoreLink);
                    },
                    child: Text(
                      "Get the app",
                      style: AppTextStyle.access1(
                        textColor: AppColors.appWhite,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),

                  // Close button
                  InkWell(
                    onTap: () {
                      downloadAppBannerBloc.onTapCloseButton();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: const Icon(
                        Icons.close,
                        color: AppColors.appBlack,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return const SizedBox();
        });
  }
//endregion
}
