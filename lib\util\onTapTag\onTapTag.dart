import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class OnTapTag{

  BuildContext context;
  String tagData;

  OnTapTag(this.context,this.tagData){
    getStoreAndUserReference();
  }


  //region Get Store and user reference
  Future<void> getStoreAndUserReference() async {

    StatefulWidget screen ;
    try {
      String reference = await StoreAndUserReferenceServices().getStoreAndUserReferences(handleAndUserName: tagData.replaceFirst("@", ""));

      //If reference is null
      if(reference ==""){
        screen = const InvalidScreen();
        var route = MaterialPageRoute(builder: (context) => screen);
        context.mounted?Navigator.push(context, route):null;
        return;
      }

      //If reference is U then add data to user reference
      if(reference.split("").first.contains("U")){
        screen = UserProfileScreen(userReference:reference,);
        var route = MaterialPageRoute(builder: (context) => screen);
        context.mounted?Navigator.push(context, route):null;
        return;
      }
      //If reference is S then add data to user reference
      if(reference.split("").first.contains("S")){
        screen = BuyerViewStoreScreen(storeReference:reference,isStoreOwnerView:AppConstants.appData.storeReference==null?false:AppConstants.appData.storeReference! == reference);
        var route = MaterialPageRoute(builder: (context) => screen);
        context.mounted?Navigator.push(context, route):null;
        return;
      }
      // if(reference.isEmpty){
      //
      // }


    } on ApiErrorResponseMessage catch(error) {
      CommonMethods.toastMessage(error.message.toString(),AppConstants.userStoreCommonBottomNavigationContext);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage,AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
//endregion

}