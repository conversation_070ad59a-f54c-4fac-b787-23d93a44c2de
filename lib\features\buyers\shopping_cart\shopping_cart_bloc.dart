import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:rich_text_controller/rich_text_controller.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/available_to/available_to_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/cart_delivery_address_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/colsed_and_deleted_store_product.dart';
import 'package:swadesic/features/buyers/shopping_cart/save_cart_notes.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/create_order_and_initiate_payment_response.dart';
import 'package:swadesic/model/shopping_cart_address_response/shopping_cart_address_response.dart';
import 'package:swadesic/model/shopping_cart_responses/bank_list_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_items_responses.dart';
import 'package:swadesic/model/shopping_cart_responses/order_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/payment_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum CartDetailState { Loading, Success, Failed, Empty }
enum CartPriceState { Loading, Success, Failed, Empty }

class ShoppingCartBloc {
  // region Common Variables
  BuildContext context;
  String addressId = "";
  bool isNoteChanged = false;
  static List<int> quantityList = <int>[1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  bool hasInitialized = false;
  ///Address Response
  late ShoppingCartAddressResponse userAddressResponse;
  late ShoppingCartAddress selectedAddress = ShoppingCartAddress() ;

  ///User Details
  // late UserDetailsServices userDetailsServices;
  // late GetUserDetailsResponse userDetailsResponse;

  ///Cart Items Response
  late GetCartItemResponses getCartItemResponses = GetCartItemResponses();

  ///Cart Details Response
  late GetCartDetailsResponse cartDetailsResponse = GetCartDetailsResponse();

  ///Cart All Response
  // late cart_full_response.CartFullResponse cartFullResponse;

  ///Order Create and initiate response
  late CreateOrderAndInitiatePaymentResponse createOrderAndInitiatePaymentResponse;

  ///Payment Create response
  late PaymentCreateResponse paymentCreateResponse;

  ///Bank List response
  late BankListResponse bankListResponse;

  ///Shopping Cart price response
  late GetCartPriceResponse getCartPriceResponse = GetCartPriceResponse();

  ///Store Product Services
  late StoreProductServices storeProductServices;

  ///Cache service
  late CacheStorageService cacheStorageService;

  ///Cart quantity data model
  late ShoppingCartQuantityDataModel shoppingCartQuantityDataModel;
  // endregion

  //region Controller
  final cartDetailStateCtrl = StreamController<CartDetailState>.broadcast();
  final quantityCtrl = StreamController<bool>.broadcast();
  final cartPriceStateCtrl = StreamController<CartPriceState>.broadcast();

  //endregion

  //region Cart Text Editing Controller
  // final TextEditingController availableToTextCtrl = TextEditingController();

  //endregion


  // region | Constructor |
  ShoppingCartBloc(this.context);

  // endregion

  // region Init
  init() async {
    //Cart quantity data model
    shoppingCartQuantityDataModel = Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);

    selectedAddress = ShoppingCartAddress();

    ///Store product service
    storeProductServices = StoreProductServices();

    ///Cache service
    cacheStorageService = CacheStorageService();

    ///User Details Service Initialization
    // userDetailsServices = UserDetailsServices();
    // ///Cart All Response
    // cartFullResponse = cart_full_response.CartFullResponse();
    ///Get Cart Items
    await getCartItems();
    // //Get shopping cart Price
    // await getShoppingCartPrice();

    ///Get User Info
    //  getUserInfo();
    ///Get User Address
    getAddress();
  }

// endregion


  //region Get Shopping cart Items
  Future<void> getCartItems() async {
    //region Try
    try {
      ///1
      ///Get cart item
      getCartItemResponses = await ShoppingCartServices().getShoppingCartItems();
      //If all cart items are empty then clear all product reference in shoppingCartQuantityDataModel and return
      if(getCartItemResponses.cartItemId!.isEmpty){
        //Empty
        cartDetailStateCtrl.sink.add(CartDetailState.Empty);
        //Clear product references from data model class
        shoppingCartQuantityDataModel.clearDara();
        return;
      }
      //If cart item is not empty then call the api to get shopping cart detail
      //Also take out product reference and add to the data model
      if(getCartItemResponses.cartItemId!.isNotEmpty){
        ///2
        ///Get cart detail
        cartDetailsResponse =  await ShoppingCartServices().getCartDetail(getCartItemResponses.cartItemId!);
        //Clear product references from data model class
        shoppingCartQuantityDataModel.productReferenceList.clear();
        //Add product reference in cart quantity data model
        //Store loop
        for(var store in cartDetailsResponse.cartStoreList!){
          //Product loop
          for(var product in store.cartProductList!){
            //If cart is not empty then add the quantity in data model
            ///Save cart quantity data to model
            shoppingCartQuantityDataModel.updateCartQuantity(productReference:product.productReference! );

          }

        }

        //Get cart message if saved
        getMessageToSharePref();
        //Success
        cartDetailStateCtrl.sink.add(CartDetailState.Success);
        getShoppingCartPrice();
      }
      //If cart item is empty
      else{
        //Clear product references from data model class
        shoppingCartQuantityDataModel.productReferenceList.clear();
        //Empty
        cartDetailStateCtrl.sink.add(CartDetailState.Empty);
      }
    }
    //endregion
    on ApiErrorResponseMessage catch(error) {
      //Failed
      cartDetailStateCtrl.sink.add(CartDetailState.Failed);
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
    } catch (error) {
      //Failed
      cartDetailStateCtrl.sink.add(CartDetailState.Failed);
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  ///Cart price
  //region Get Shopping cart Price
  Future<void> getShoppingCartPrice() async {
    //region Try
    try {
      //Loading
      cartPriceStateCtrl.sink.add(CartPriceState.Loading);
     getCartPriceResponse = await ShoppingCartServices().getCartPrice(getCartItemResponses.cartItemId!);
    //Success
    cartPriceStateCtrl.sink.add(CartPriceState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error){
      //Failed
      cartPriceStateCtrl.sink.add(CartPriceState.Failed);
      //Message
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
    } catch (error) {
      //Failed
      cartPriceStateCtrl.sink.add(CartPriceState.Failed);
      //Message
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  //region Delete Shopping Cart Item
  deleteShoppingCartItem({required CartStore cartStore, required CartProduct products}) async {
    //region Try
    try {
      //Remove cart items id from getCartItemResponses
      getCartItemResponses.cartItemId!.removeWhere((element) => element == products.cart!.cartItemId!);
      //Remove Cart if from Cart Id response
      cartStore.cartProductList!.removeWhere((element) => element.cart!.cartItemId == products.cart!.cartItemId!);
      //Remove products inside the store
      cartStore.cartProductList!.removeWhere((element) => element.cart!.cartItemId == products.cart!.cartItemId!);
      //Remove store where products are empty
      cartDetailsResponse.cartStoreList!.removeWhere((element) => element.cartProductList!.isEmpty);
      //Remove cart Item API call
      await ShoppingCartServices().deleteCartItem(products.cart!.cartItemId!);
      //Call init
      init();
      //Get Cart Detail
      //getCartDetails();
      //Get Cart price
      // getShoppingCartPrice();
      //Loading
      // shoppingCartCtrl.sink.add(ShoppingCartState.Success);
    }
    //endregion
      on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
      return;
      //print(error.message);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //endregion

  List<Map> collectSellerNotes() {
    List<Map> data = [];

    for (var uni in cartDetailsResponse.cartStoreList!) {
      data.add({"storeid": uni.storeid, "note": uni.sellerNote});
    }
    data.removeWhere((element) => element["note"] == "");

    //print(data);
    return data;
  }



  //region Go to secure check out
  void goToSecureCheckout(){
    var screen = SecureCheckoutScreen(shoppingCartBloc: this,);
    // var screen = BuyerViewStoreScreen(storeReference: "S4185590",);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // init();
      // //Success
      // cartDetailStateCtrl.sink.add(CartDetailState.Success);
    });
  }
  //endregion


  //region From store id to CartStore
  List<CartStore> storeIdToCartStore(){
    List<CartStore> cartStoreList = [];
    //Store id loop
    for(var storeId in createOrderAndInitiatePaymentResponse.closedStore!){
      cartStoreList.addAll(cartDetailsResponse.cartStoreList!.where((element) => element.storeid == storeId));
    }
    return cartStoreList;
  }
  //endregion


  //region Take out deleted products
  List<CartProduct> deletedProduct(){
    List<CartStore> deletedStore = [];
    List<CartProduct> deletedCartProductList = [];

   //Deleted store by finding deleted store id
    for(var storeId in createOrderAndInitiatePaymentResponse.deletedStore!){
      // deletedStore = cartDetailsResponse.cartStoreList!.where((element) => element.storeid == storeId).toList();
      deletedStore.addAll(cartDetailsResponse.cartStoreList!.where((element) => element.storeid == storeId));
    }

    //Deleted product from deleted store
    for(var store in deletedStore){
      deletedCartProductList.addAll(store.cartProductList!.where((element) => element.isDeleted!).toList());
    }

    //Deleted product from open store
    for(var store in cartDetailsResponse.cartStoreList!){
      //print(store.storeName);
      deletedCartProductList.addAll(store.cartProductList!.where((element) => createOrderAndInitiatePaymentResponse.deletedProducts!.contains(element.productReference)));
      // deletedCartProductList = store.cartProductList!.where((element) => orderCreateResponse.deletedProducts!.contains(element.productReference)).toList();
    }
    return deletedCartProductList;
  }
  //endregion

  //region On Change Quantity
  void onChangeQuantity({required CartProduct products, required int quantity}) async {
    //Update quantity
    products.cart!.quantity = quantity;
    //Refresh quantity drop down
    quantityCtrl.sink.add(true);
    await updateQuantityApiCall(products.cart!.quantity!, products.cart!.cartItemId!);
    //Get cart Price
    getShoppingCartPrice();
    //Success
    cartDetailStateCtrl.sink.add(CartDetailState.Success);
  }

  //endregion

  //region Update Quantity Api Call
  updateQuantityApiCall(int quantity, int cartItemId) async {
    //region Try
    try {
      await ShoppingCartServices().updateProductQuantity(quantity, cartItemId);
      //Success
      cartDetailStateCtrl.sink.add(CartDetailState.Success);
    }
    //endregion
      on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    } catch (error) {
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  //region On tap drop down
  void onTapDropDown({required CartStore cartStore}) {
    cartStore.isDropDown = !cartStore.isDropDown;
    cartDetailStateCtrl.sink.add(CartDetailState.Success);
  }
  //endregion

  ///Add to wish list
  //region Add to wish list Api
  Future<void>addToWishListApi({required List<String> productReferenceList}) async {
    //region Try
    try {
      //Add wishlist api call
       ShoppingCartServices().addToWishListApiCall(productReferenceList: productReferenceList);
    }
    //endregion
      on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
    } catch (error) {
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  ///Remove product from cart
  //region Remove multiple cart items Api
  Future<void>removeMultipleCartItemsApi({required List<int> cartItemIdList}) async {
    //region Try
    try {

      //Api call
      await ShoppingCartServices().removeMultipleCartItems(cartItemIdList: cartItemIdList);


    }
    //endregion
      on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
    } catch (error) {
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  //region Get Addresses
  getAddress() async {
    //region Try
    try {
      // selectedAddressRefreshCtrl.sink.add(ShoppingAddressState.Loading);
      userAddressResponse = await UserAddressService().getUserAddress();

      // selectedAddressRefreshCtrl.sink.add(ShoppingAddressState.Success);
    }
    //endregion
      on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

    }
  }
  //endregion

  ///Not in use
  //region Not in use


  //region Add Address Api call
  // void addAddressApiCall() async {
  //   //region Try
  //   try {
  //     if (addressTextCtrl.text.isEmpty || firstNameTextCtrl.text.isEmpty) {
  //       CommonMethods.toastMessage("Field can't be empty", context);
  //       return;
  //     }
  //     //Pin code check
  //     if(pinCodeTextCtrl.text.length < 6){
  //       CommonMethods.toastMessage(AppStrings.enterValidPinCode, context);
  //       return;
  //     }
  //     Navigator.of(context, rootNavigator: true).pop();
  //     Navigator.of(context, rootNavigator: true).pop();
  //     await userAddressService.addAddress(addressTextCtrl.text, cityTextCtrl.text, pinCodeTextCtrl.text, stateTextCtrl.text, firstNameTextCtrl.text,
  //         false, int.parse(phoneNumberTextCtrl.text));
  //     getAddress();
  //   }
  //   //endregion
  //     on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     return;
  //     return;
  //     //print(error.message);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //
  //     return;
  //   } catch (error) {
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //
  //     return;
  //   }
  // }

  //endregion

  //region Edit Address Api call
  // void editAddressApiCall() async {
  //   //region Try
  //   try {
  //     if (addressTextCtrl.text.isEmpty || firstNameTextCtrl.text.isEmpty) {
  //       CommonMethods.toastMessage("Field can't be empty", context);
  //       return;
  //     }
  //     Navigator.of(context, rootNavigator: true).pop();
  //     Navigator.of(context, rootNavigator: true).pop();
  //     Navigator.of(context, rootNavigator: true).pop();
  //
  //     await userAddressService.editAddress(
  //         addressTextCtrl.text,
  //         cityTextCtrl.text,
  //         pinCodeTextCtrl.text,
  //         stateTextCtrl.text,
  //         firstNameTextCtrl.text,
  //         false,
  //         int.parse(phoneNumberTextCtrl.text),
  //         int.parse(addressId));
  //
  //     //Updated data to selected address
  //     selectedAddress.address = addressTextCtrl.text;
  //     selectedAddress.city = cityTextCtrl.text;
  //     selectedAddress.pincode = pinCodeTextCtrl.text;
  //     selectedAddress.state = stateTextCtrl.text;
  //     selectedAddress.name = firstNameTextCtrl.text;
  //     selectedAddress.phoneNumber = phoneNumberTextCtrl.text;
  //
  //
  //
  //
  //
  //     //Get address
  //     getAddress();
  //   }
  //   //endregion
  //     on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     return;
  //     return;
  //     //print(error.message);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //
  //     return;
  //   } catch (error) {
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //
  //     return;
  //   }
  // }

  //endregion

  //region Delete Address Api call
  // void deleteAddress() async {
  //   //region Try
  //   try {
  //     Navigator.of(context, rootNavigator: true).pop();
  //     Navigator.of(context, rootNavigator: true).pop();
  //     await userAddressService.deleteAddress(int.parse(addressId));
  //     init();
  //   }
  //   //endregion
  //     on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     return;
  //     return;
  //     //print(error.message);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //
  //     return;
  //   } catch (error) {
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //
  //     return;
  //   }
  // }

  //endregion

  //region Address to the Field
  // void addressToTheField(int index) {
  //   firstNameTextCtrl.text = userAddressResponse.addressList![index].name!;
  //   phoneNumberTextCtrl.text = userAddressResponse.addressList![index].phoneNumber!;
  //   addressTextCtrl.text = userAddressResponse.addressList![index].address!;
  //   cityTextCtrl.text = userAddressResponse.addressList![index].city!;
  //   pinCodeTextCtrl.text = userAddressResponse.addressList![index].pincode!;
  //   stateTextCtrl.text = userAddressResponse.addressList![index].state!;
  // }

  //endregion
//endregion

  //region Show we saved your note
    showWeSavedYourNote() {
    if(isNoteChanged){
      saveMessageToSharePref();
      CommonMethods.toastMessage(AppStrings.weGotYourBack, context);
      Navigator.pop(context);
    }
    else{
      Navigator.pop(context);
    }
    }

  //endregion

  //region Save address id
  void saveAddressId({required int addressId}) {
    SaveCartNotes saveCartNotes = SaveCartNotes(cartDetailsResponse: cartDetailsResponse, context: context, cacheStorageService: cacheStorageService);

    saveCartNotes.saveAddressId(addressId: addressId.toString());
  }
  //endregion

  //region Get saved address
  // void getSavedAddress() async {
  //   SaveCartNotes saveCartNotes = SaveCartNotes(cartDetailsResponse: cartDetailsResponse, context: context, cacheStorageService: cacheStorageService);
  //   String addressIdFromSharePref = await saveCartNotes.getAddressId();
  //   selectedAddress = userAddressResponse.addressList!.firstWhere((element) => element.useraddressid == int.parse(addressIdFromSharePref));
  //   addressId = addressIdFromSharePref;
  //   // availableToTextCtrl.text = selectedAddress.pincode!;
  //   onSelectAddressCtrl.sink.add(int.parse(addressId));
  //   selectedAddressRefreshCtrl.sink.add(ShoppingAddressState.Success);
  // }

  //endregion

  //region Get messages to share pref
  void getMessageToSharePref() async {
    SaveCartNotes saveCartNotes = SaveCartNotes(cartDetailsResponse: cartDetailsResponse, context: context, cacheStorageService: cacheStorageService);
    saveCartNotes.getCartDetailToSharePref();
  }

//endregion

  //region Save messages to share pref
  void saveMessageToSharePref() async {
    SaveCartNotes saveCartNotes = SaveCartNotes(cartDetailsResponse: cartDetailsResponse, context: context, cacheStorageService: cacheStorageService);

    saveCartNotes.saveCartDetailToSharePref();
    // saveCartNotes.getCartDetailToSharePref();
  }

//endregion

  //region Go to store
    void goToStore({required String storeReference}) {
      var screen = BuyerViewStoreScreen(
        storeReference: storeReference,
      );
      // var screen = BuyerViewStoreScreen(storeReference: "S4185590",);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route).then((value) {
        // screenRefreshCtrl.sink.add(true);
      });
    }
  //endregion

}
