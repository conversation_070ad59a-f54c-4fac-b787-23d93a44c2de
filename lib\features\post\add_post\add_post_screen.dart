import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/post/add_post/add_post_bloc.dart';
import 'package:swadesic/features/post/add_post/widgets/typing_suggestions_overlay.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddPostScreen extends StatefulWidget {
  const AddPostScreen({super.key});

  @override
  State<AddPostScreen> createState() => _AddPostScreenState();
}

class _AddPostScreenState extends State<AddPostScreen> {
  //Bloc
  late AddPostScreenBloc addPostScreenBloc;

  //region Init
  @override
  void initState() {
    addPostScreenBloc = AddPostScreenBloc(context);
    addPostScreenBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    addPostScreenBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: StreamBuilder<AddPostScreenState>(
          stream: addPostScreenBloc.addUpdatePostScreenStateCtrl.stream,
          builder: (context, snapshot) {
            return Stack(
              children: [
                Scaffold(
                  floatingActionButton: postButton(),
                  floatingActionButtonLocation:
                      FloatingActionButtonLocation.endFloat,
                  appBar: appBar(),
                  body: SafeArea(child: body()),
                ),
                Visibility(
                  visible: snapshot.data == AddPostScreenState.Loading,
                  child: Positioned.fill(
                    child: Container(
                      color: AppColors.textFieldFill1.withOpacity(0.8),
                      alignment: Alignment.center,
                      child: AppCommonWidgets.appCircularProgress(),
                    ),
                  ),
                )
              ],
            );
          }),
    );
  }

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        Navigator.pop(context);
      },
      context: context,
      isCustomTitle: false,
      title: AppStrings.createPost,
      isMembershipVisible: false,
      isCustomMenuVisible: true,
      isDefaultMenuVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          userInfo(),
          postImages(),
          writeYourThough(),
          access(),
        ],
      ),
    );
  }

//endregion

  //region Widget user info

  Widget userInfo() {
    final loggedInUser = Provider.of<LoggedInUserInfoDataModel>(
        context); // Access the provided data
    final loggedInStore = Provider.of<SellerOwnStoreInfoDataModel>(
        context); // Access the provided data
    // final value = context.watch<LoggedInUserInfoDataModel?>();
    // final value = context.watch<SellerOwnStoreInfoDataModel?>();

    return Container(
      alignment: Alignment.centerLeft,
      margin: const EdgeInsets.only(
        bottom: 5,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(CommonMethods.byReferenceIsUSer(
                    reference: AppConstants.appData.isUserView!
                        ? AppConstants.appData.userReference!
                        : AppConstants.appData.storeReference!)
                ? 100
                : (0.4130 * 27)),
            child: SizedBox(
              height: 27,
              width: 27,
              child: extendedImage(
                  AppConstants.appData.isUserView!
                      ? loggedInUser.userDetail!.icon
                      : loggedInStore.storeInfo!.icon,
                  context,
                  100,
                  100,
                  customPlaceHolder: AppImages.userPlaceHolder),
            ),
          ),
          const SizedBox(
            width: 5,
          ),
          Text(
            AppConstants.appData.isUserView!
                ? loggedInUser.userDetail!.userName ?? "user_name"
                : loggedInStore.storeInfo!.storehandle ?? "Store",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
                .copyWith(height: 0),
          ),
          VerifiedBadge(
            width: 15,
            height: 15,
            subscriptionType: AppConstants.appData.isUserView!
                ? loggedInUser.userDetail!.subscriptionType
                : loggedInStore.storeInfo!.subscriptionType,
          ),
        ],
      ),
    );
  }

//endregion

  //region Post images
  Widget postImages() {
    // For web platform
    if (kIsWeb) {
      return StreamBuilder<List<Map<String, dynamic>>>(
        stream: addPostScreenBloc.webImagesCtrl.stream,
        initialData: const [],
        builder: (context, snapshot) {
          //If not empty
          if (snapshot.data!.isNotEmpty) {
            return SizedBox(
              height: 236,
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) {
                  final imageData = snapshot.data![index];
                  return Stack(
                    children: [
                      //Image
                      PostAndProductImageWidgets(
                        localOrNetworkImage: imageData['name'],
                        webImageBytes: imageData['bytes'],
                      ),
                      //Remove icon
                      Positioned(
                        right: 15,
                        top: 5,
                        child: SizedBox(
                          height: 30,
                          width: 30,
                          child: CupertinoButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              addPostScreenBloc.onTapRemoveWebImage(
                                  index: index);
                            },
                            child: Opacity(
                              opacity: 0.5,
                              child: SvgPicture.asset(AppImages.removeCircle1),
                            ),
                          ),
                        ),
                      )
                    ],
                  );
                },
              ),
            );
          }
          return const SizedBox();
        },
      );
    }

    // For mobile platform
    return StreamBuilder<List<File>>(
      stream: addPostScreenBloc.selectedImageCtrl.stream,
      initialData: const [],
      builder: (context, snapshot) {
        //If not empty
        if (snapshot.data!.isNotEmpty) {
          return SizedBox(
            height: 236,
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              scrollDirection: Axis.horizontal,
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    //Image
                    PostAndProductImageWidgets(
                      localOrNetworkImage: snapshot.data![index].path,
                    ),
                    //Remove icon
                    Positioned(
                      right: 15,
                      top: 5,
                      child: SizedBox(
                        height: 30,
                        width: 30,
                        child: CupertinoButton(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            addPostScreenBloc.onTapRemoveImage(
                                filePath: snapshot.data![index]);
                          },
                          child: Opacity(
                            opacity: 0.5,
                            child: SvgPicture.asset(AppImages.removeCircle1),
                          ),
                        ),
                      ),
                    )
                  ],
                );
              },
            ),
          );
        }
        return const SizedBox();
      },
    );
  }
  //endregion

//region Write your though
  Widget writeYourThough() {
    return Column(
      children: [
        TextFormField(
            autofocus: true,
            maxLines: 10,
            minLines: 3,
            textCapitalization: TextCapitalization.sentences,
            inputFormatters: [
              LengthLimitingTextInputFormatter(500),
            ],
            textAlign: TextAlign.start,
            textInputAction: TextInputAction.none,
            keyboardType: TextInputType.multiline,
            controller: addPostScreenBloc.addPostTextCtrl,
            scrollPadding: EdgeInsets.only(
                bottom: MediaQuery.of(context).size.height * 0.2),
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            decoration: InputDecoration(
              isDense: true,
              hintStyle:
                  AppTextStyle.hintText(textColor: AppColors.writingBlack1),
              fillColor: AppColors.appWhite,
              // Specify the desired internal color
              filled: true,
              hintText: "write your thoughts..",
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
              border: InputBorder.none, // Remove all borders
            )),
        // Typing suggestions overlay
        StreamBuilder<bool>(
          stream: addPostScreenBloc.showSuggestionsCtrl.stream,
          initialData: false,
          builder: (context, showSnapshot) {
            if (!showSnapshot.data!) {
              return const SizedBox.shrink();
            }

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 14.0),
              child: StreamBuilder<List<SuggestionItem>>(
                stream: addPostScreenBloc.suggestionsCtrl.stream,
                initialData: const [],
                builder: (context, suggestionsSnapshot) {
                  return StreamBuilder<bool>(
                    stream: addPostScreenBloc.suggestionsLoadingCtrl.stream,
                    initialData: false,
                    builder: (context, loadingSnapshot) {
                      return TypingSuggestionsOverlay(
                        suggestions: suggestionsSnapshot.data ?? [],
                        onSuggestionTap: addPostScreenBloc.onSuggestionTap,
                        isLoading: loadingSnapshot.data ?? false,
                        onLoadMore: addPostScreenBloc.loadMoreSuggestions,
                        hasMore: addPostScreenBloc.hasMoreSuggestions,
                      );
                    },
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

//endregion

//region Access
  Widget access() {
    return Column(
      children: [
        //Add photos
        Consumer<AppConfigDataModel>(
          builder:
              (BuildContext context, AppConfigDataModel value, Widget? child) {
            return AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionText:
                  "${AppStrings.addPhotos} (up to ${value.appConfig!.postImageLimit})",
              onTap: () {
                addPostScreenBloc.onTapAddImage();
              },
            );
          },
        ),
        Consumer<AppConfigDataModel>(
          builder:
              (BuildContext context, AppConfigDataModel value, Widget? child) {
            return AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionText:
                AppStrings.tagStoreProductMember,
              onTap: () {
                // add tag functionality here 
              },
            );
          },
        ),

        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              optionText: AppStrings.addGifVideo,
              arrowColor: AppColors.disableBlack,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              arrowColor: AppColors.disableBlack,
              optionText: AppStrings.addLocation,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              arrowColor: AppColors.disableBlack,
              optionText: AppStrings.tagStorePeople,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
        AppToolTip(
          message: AppStrings.thisFeatureIsCommingSoon,
          toolTipWidget: IgnorePointer(
            child: AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionTextColor: AppColors.disableBlack,
              arrowColor: AppColors.disableBlack,
              optionText: AppStrings.promoteProduct,
              onTap: () {
                // Add your onTap functionality here
              },
            ),
          ),
        ),
      ],
    );
  }

//endregion

//region Post button
  Widget postButton() {
    return FloatingActionButton.extended(
        backgroundColor: AppColors.appWhite,
        extendedPadding: EdgeInsets.zero,
        isExtended: true,
        elevation: 0,
        tooltip: AppStrings.postButton,
        label: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            ///Post
            Row(
              children: [
                CupertinoButton(
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.circular(100),
                  onPressed: () {
                    //If non register user
                    if (CommonMethods().isStaticUser()) {
                      CommonMethods().goToSignUpFlow();
                      return;
                    }

                    addPostScreenBloc.addPost();
                  },
                  padding:
                      const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
                  child: Text(
                    AppStrings.postButton,
                    style: AppTextStyle.access0(textColor: AppColors.appWhite),
                  ),
                ),
              ],
            ),
          ],
        ),
        onPressed: null);
  }
//endregion
}

// CupertinoButton(
// color: AppColors.brandGreen,
// borderRadius: BorderRadius.circular(100),
// onPressed: () {
// // sellerStoreDeliverySettingBloc.onTapSave();
// },
// padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
// child: Text(
// AppStrings.saveChanges,
// style: AppTextStyle.access0(textColor: AppColors.appWhite),
// ),
// ),
