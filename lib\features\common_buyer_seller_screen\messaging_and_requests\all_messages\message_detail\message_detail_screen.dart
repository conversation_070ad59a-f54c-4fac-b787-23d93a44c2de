import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_field.dart';
import 'package:swadesic/features/data_model/message_chat_data_model/message_chat_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/messaging_cards/message_chat_bubble.dart';
import 'package:swadesic/model/messaging_response/messaging_detail.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'message_detail_bloc.dart';
import 'package:flutter/cupertino.dart';

//region Message Detail Screen
class MessageDetailScreen extends StatefulWidget {
  final String toEntityReference;
  const MessageDetailScreen({Key? key, required this.toEntityReference})
      : super(key: key);

  @override
  State<MessageDetailScreen> createState() => _MessageDetailScreenState();
}
//endregion

//region Message Detail Screen State
class _MessageDetailScreenState extends State<MessageDetailScreen> {
  //region Variables
  late MessageDetailBloc messageDetailBloc;
  //endregion

  //region Init
  @override
  void initState() {
    super.initState();
    messageDetailBloc = MessageDetailBloc(context, widget.toEntityReference);
    messageDetailBloc.init();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    messageDetailBloc.dispose();
    super.dispose();
  }
  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        //Close keyboard
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        appBar: appBar(),
        body: body(),
      ),
    );
  }
  //endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,

      customTitleWidget: StreamBuilder<AppStateEnum>(
          stream: messageDetailBloc.messageDetailStateCtrl.stream,
          initialData: AppStateEnum.Loading,
          builder: (context, snapshot) {
            //Success or Empty
            if (snapshot.data == AppStateEnum.Success ||
                snapshot.data == AppStateEnum.Empty) {
              return InkWell(
                onTap: () {
                  messageDetailBloc
                          .messageDetailResponse.data!.toProfile!.reference!
                          .startsWith('S')
                      ? messageDetailBloc.goToStore(
                          storeReference: messageDetailBloc
                              .messageDetailResponse
                              .data!
                              .toProfile!
                              .reference!)
                      : messageDetailBloc.goToUserProfile(
                          userReference: messageDetailBloc.messageDetailResponse
                              .data!.toProfile!.reference!);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CustomImageContainer(
                        width: 40,
                        height: 40,
                        imageUrl: messageDetailBloc
                            .messageDetailResponse.data!.toProfile!.icon,
                        imageType: messageDetailBloc.messageDetailResponse.data!
                                .toProfile!.reference!
                                .startsWith('S')
                            ? CustomImageContainerType.store
                            : CustomImageContainerType.user),
                    const SizedBox(
                      width: 15,
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          messageDetailBloc
                              .messageDetailResponse.data!.toProfile!.handle!,
                          style: AppTextStyle.settingHeading1(
                              textColor: AppColors.appBlack),
                        ),
                        Text(
                          messageDetailBloc
                              .messageDetailResponse.activeStatusString!,
                          style: AppTextStyle.smallText(
                              textColor: messageDetailBloc.messageDetailResponse
                                          .activeStatusString!
                                          .toLowerCase() ==
                                      "online"
                                  ? AppColors.brandBlack
                                  : AppColors.writingBlack1),
                        ),
                      ],
                    )
                  ],
                ),
              );
            }

            return const SizedBox();
          }),
      // titleWidget: Text(UserProfileBloc.getUserDetailsResponse.userDetail!.userName!??"", style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 19, fontWeight: FontWeight.w700, color: AppColors.appBlack)),
      isDefaultMenuVisible: true,
      isCartVisible: false,
      isCustomTitle: true,
      isLeadingVisible: true,
    );
  }

//endregion

//region Profile Card
  Widget _buildProfileCard() {
    final profile = messageDetailBloc.messageDetailResponse.data!.toProfile!;
    final isStore = profile.reference!.startsWith('S');

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Profile Image
          CustomImageContainer(
            width: 80,
            height: 80,
            imageUrl: profile.icon,
            imageType: isStore
                ? CustomImageContainerType.store
                : CustomImageContainerType.user,
          ),
          const SizedBox(height: 12),

          // Name and Supporters
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                profile.name ?? '',
                style:
                    AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${profile.followerOrSupporterCount ?? 0} ${isStore ? 'supporters' : 'followers'}',
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack1),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // View Store/Profile Button
          Center(
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                if (isStore) {
                  messageDetailBloc.goToStore(
                      storeReference: profile.reference!);
                } else {
                  messageDetailBloc.goToUserProfile(
                      userReference: profile.reference!);
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 7, horizontal: 12),
                decoration: const BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: Text(isStore ? 'View store' : 'View profile',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.access0(
                        textColor: AppColors.writingBlack0)),
              ),
            ),
          ),
        ],
      ),
    );
  }
  //endregion

//region Body
  Widget body() {
    return Column(
      children: [
        Expanded(
          child: StreamBuilder<AppStateEnum>(
              stream: messageDetailBloc.messageDetailStateCtrl.stream,
              initialData: AppStateEnum.Loading,
              builder: (context, snapshot) {
                //print(snapshot.data);
                //Loading
                if (snapshot.data == AppStateEnum.Loading) {
                  return AppCommonWidgets.appCircularProgress();
                }
                //Empty
                if (snapshot.data == AppStateEnum.Empty) {
                  return ListView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    children: [
                      _buildProfileCard(),
                      SizedBox(height: MediaQuery.of(context).size.width / 4),
                      SvgPicture.asset(
                        AppImages.messageIcon,
                        width: 100,
                        height: 100,
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "No messages yet",
                            textAlign: TextAlign.center,
                            style: AppTextStyle.contentText0(
                                textColor: AppColors.writingBlack1),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          SvgPicture.asset(AppImages.smileEmoji)
                        ],
                      )
                    ],
                  );
                }
                //Success
                if (snapshot.data == AppStateEnum.Success) {
                  return messageList();
                }
                //Failed
                return AppCommonWidgets.errorWidget(onTap: () {
                  messageDetailBloc.init();
                });
              }),
        ),
        MessageDetailField(
          controller: messageDetailBloc.messageController,
          onSend: (String message, List<Map<String, dynamic>> attachments) {
            messageDetailBloc.addMessageWithAttachments(message, attachments);
          },
        )
      ],
    );
  }
//endregion

//region Message list
  Widget messageList() {
    return Consumer<MessageChatDataModel>(
      builder:
          (BuildContext context, MessageChatDataModel value, Widget? child) {
        // List<ChatDetail> chatList = [];
        // chatList = value.chatDetailList.where((element) => messageDetailBloc.messagesList.any((e) => e.messageId == element.messageId)).toList();
        // //Sort chatList old to latest and by IST time format
        // chatList.sort((a, b) => a.timestamp!.compareTo(b.timestamp!));

        List<ChatDetail> chatList = [];
        chatList = value.chatDetailList
            .where((element) => messageDetailBloc.messagesList
                .any((e) => e.messageId == element.messageId))
            .toList();

        // Sort chatList old to latest and by IST time format
        chatList.sort((a, b) {
          // Convert timestamps to IST (UTC + 5:30)
          var aIST = DateTime.parse(a.timestamp!)
              .toUtc()
              .add(Duration(hours: 5, minutes: 30));
          var bIST = DateTime.parse(b.timestamp!)
              .toUtc()
              .add(Duration(hours: 5, minutes: 30));

          // Sort in ascending order (oldest to newest)
          return aIST.compareTo(bIST);
        });
        //print("Total data leangth is ${chatList.length}");
        return ScrollConfiguration(
          behavior: const ScrollBehavior(),
          child: SingleChildScrollView(
            controller: messageDetailBloc.scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildProfileCard(),
                paginationProgress(),
                ListView.builder(
                  key: ValueKey(chatList.length),
                  physics: const NeverScrollableScrollPhysics(),
                  padding:
                      const EdgeInsets.only(left: 10, right: 10, bottom: 30),
                  itemCount: chatList.length,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    String senderReference = AppConstants.appData.isUserView!
                        ? AppConstants.appData.userReference!
                        : AppConstants.appData.storeReference!;

                    // Check if there's a previous message and compare sender references
                    bool isPreviousSame = index > 0 &&
                        chatList[index - 1].senderReference! ==
                            chatList[index].senderReference;

                    // Check if there's a next message and compare sender references
                    bool isNextSame = index < chatList.length - 1 &&
                        chatList[index + 1].senderReference ==
                            chatList[index].senderReference;

                    ChatDetail message = chatList[index];
                    print("message.attachments: ${message.attachments}");
                    return LayoutBuilder(
                      key: ValueKey(message.messageId),
                      builder:
                          (BuildContext context, BoxConstraints constraints) {
                        // Convert ChatAttachment list to List<Map<String, dynamic>>

                        final attachments = message.attachments
                                ?.map((attachment) => {
                                      'originalName': attachment.originalName,
                                      'filename': attachment.filename,
                                      'type': attachment.type,
                                      'size': attachment.size,
                                      'url': attachment.url,
                                      'mimetype': attachment.mimetype
                                    })
                                .toList() ??
                            [];
                        print("attachments: ${attachments}");

                        return MessageChatBubble(
                          dateTime: message.timestamp!,
                          message: message.text!,
                          isSender: message.senderReference == senderReference,
                          isPreviousSame: isPreviousSame,
                          isNextSame: isNextSame,
                          boxConstraints: constraints,
                          isSent: message.isDeliveredToServer,
                          attachments: attachments,
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
//endregion

//region Pagination progress indicator
  Widget paginationProgress() {
    return StreamBuilder<AppStateEnum>(
        stream: messageDetailBloc.paginationStateCtrl.stream,
        initialData: AppStateEnum.Loading,
        builder: (context, snapshot) {
          //If current pagination state is empty then return
          if (messageDetailBloc.paginationCurrentStateCtrl ==
              AppStateEnum.Empty) {
            return Container();
          }
          //Loading
          if (snapshot.data == AppStateEnum.Loading) {
            return AppCommonWidgets.appCircularProgress(
                isPaginationProgress: true);
          }
          return Container();
        });
  }
//endregion
}
