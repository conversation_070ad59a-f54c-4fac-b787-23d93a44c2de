import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/disclaimer_bottom_sheet.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/pickup_locations_bottom_sheet.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/update_stock/update_stock.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/shopping_cart_responses/add_to_cart_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/pickup_location_service/pickup_location_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/single_product_and_image_service/single_product_and_image_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/buy_options_bottom_sheet.dart';

class BuyButtonBloc {
  //region Common variable
  late BuildContext context;
  final pickupLocationService = PickupLocationService();
  // final Product product;

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final buttonRefreshCtrl = StreamController<bool>.broadcast();
  final isButtonLoadingCtrl = StreamController<bool>.broadcast();
//endregion
  //region Constructor
  BuyButtonBloc(this.context);
  //endregion
//region Init
  void init() {}
//endregion

  //region Open update stocks
  void onTapUpdateStock({required Product product}) async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20), topLeft: Radius.circular(20)),
        ),
        builder: (context) {
          return SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Text(
                      AppStrings.updateStock,
                      style: AppTextStyle.sectionSemiBold(
                          textColor: AppColors.appBlack),
                    ),
                  ),
                  UpdateStock(
                    product: product,
                  ),
                ],
              ));
        }).then((value) {});
  }
//endregion

  //region Switch to buyer
  void switchToBuyer({required String productReference}) async {
    //Switch to buyer
    CommonMethods.switchToBuyer(context: context);
    await Future.delayed(const Duration(seconds: 2));
    //Push to product screen
    var screen =
        BuyerViewSingleProductScreen(productReference: productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
//endregion

//region Go to Shopping Cart Screen
  gotoShoppingCart() {
    //If static user then return
    if (CommonMethods().isStaticUser()) {
      CommonMethods().goToSignUpFlow();
      return;
    }
    var screen = const ShoppingCartScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      buttonRefreshCtrl.sink.add(true);
    }).then((value) {});
  }
//endregion

  // region AddToCart(int storeId,int productId,)async{
  addToCart({bool goToCart = false, required Product product}) async {
    //If product out of stock
    if (product.inStock == 0) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.productIsOutOfStock, context)
          : null;
      return;
    }

    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);

    try {
      AddToCartResponse addToCartResponse = await ShoppingCartServices()
          .addToCart(
              productReference: product.productReference!,
              storeId: product.storeid!);

      ///Add product reference to the data model
      shoppingCartQuantityDataModel.updateCartQuantity(
          productReference: product.productReference!);

      context.mounted
          ? CommonMethods.toastMessage(
              AppStrings.productHasBeenAddedToYourCart, context)
          : null;
      buttonRefreshCtrl.sink.add(true);
      if (goToCart) {
        gotoShoppingCart();
      }
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;

      return;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;

      return;
    }
  }
//endregion

  ///On tap test button
//region On tap test button
  bool onTapTestButton({required Product product}) {
    UserCreatedStoresDataModel userCreatedStoresDataModel =
        Provider.of<UserCreatedStoresDataModel>(context, listen: false);

    //If product is belongs to the owner test store and quantity is 0
    if (product.inStock == 0 &&
        userCreatedStoresDataModel.storeListResponseData!.storeList!.any((e) =>
            e.storehandle == product.storehandle &&
            product.storehandle!.startsWith("test"))) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.productIsOutOfStock, context)
          : null;
      return false;
    }
    //If product is belongs to the owner test store then return true
    if (userCreatedStoresDataModel.storeListResponseData!.storeList!.any((e) =>
        e.storehandle == product.storehandle &&
        product.storehandle!.startsWith("test"))) {
      return true;
    }
    //Else show a message
    else {
      context.mounted
          ? CommonMethods.toastMessage(
              AppStrings.orderingOnSwadesicIsCommingSoon, context)
          : null;
      return false;
    }
  }
//endregion

  ///Is okay to buy
//region Is okay ti buy
  bool isOkayToBuy({required Product product}) {
    UserCreatedStoresDataModel userCreatedStoresDataModel =
        Provider.of<UserCreatedStoresDataModel>(context, listen: false);

    //If is config receive order is false (From Swadesic purchasing off)
    if (!product.configReceiveOrders!) {
      context.mounted
          ? CommonMethods.toastMessage(
              "Ordering on Swadesic is currently unavailable. You'll be able to make purchases again soon.",
              context)
          : null;
      return false;
    }

    //Todo
    //If open for order is false
    // if(!product.openForOrder!){
    //   context.mounted?CommonMethods.toastMessage("${product.storehandle} is not accepting any orders at this time.", context):null;
    //   return false;
    // }

    //If deliver ability is false
    if (!product.deliverable!) {
      context.mounted
          ? CommonMethods.toastMessage(
              "This product cannot be delivered to your location.", context)
          : null;
      return false;
    }

    //If out of stock
    if (product.inStock == 0) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.productIsOutOfStock, context)
          : null;
      return false;
    }
    //Else return true
    else {
      return true;
    }
  }
//endregion

  //region Is button enable
  bool isButtonEnable({required Product product}) {
    UserCreatedStoresDataModel userCreatedStoresDataModel =
        Provider.of<UserCreatedStoresDataModel>(context, listen: false);

    //If is config receive order is false
    if (!product.configReceiveOrders!) {
      return false;
    }

    //If buy is not enabled
    if (!product.isBuyEnable!) {
      return false;
    }

    //If out of stock
    if (product.inStock == 0) {
      return false;
    }
    //Else return true
    else {
      return true;
    }
  }
  //endregion

  //region Is product belongs to the logged in user
  bool isProductBelongsToLoggedInUser(
      {required BuildContext context, required String storeHandle}) {
    UserCreatedStoresDataModel userCreatedStoresDataModel =
        Provider.of<UserCreatedStoresDataModel>(context, listen: false);

    //If stores are empty
    if (userCreatedStoresDataModel.storeListResponseData!.storeList!.isEmpty) {
      return false;
    } else {
      return userCreatedStoresDataModel.storeListResponseData!.storeList!.any(
          (e) =>
              e.storehandle == storeHandle && storeHandle.startsWith("test"));
    }
  }
//endregion

  //region Show pickup locations
  Future<void> showPickupLocations({required Product product}) async {
    try {
      final locations = await pickupLocationService
          .getPickupLocationsForProduct(product.productReference!);
      if (context.mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          enableDrag: true,
          backgroundColor: AppColors.appWhite,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(20), topLeft: Radius.circular(20)),
          ),
          builder: (context) =>
              PickupLocationsBottomSheet(locations: locations.data ?? []),
        );
      }
    } catch (e) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }
  //endregion

  //region Get button text
  String getButtonText({required Product product}) {
    if (product.fulfillmentOptions == "IN_STORE_PICKUP") {
      return "Buy At Store";
    }
    return buttonNames(product: product);
  }
  //endregion

  //region Button names
  String buttonNames({required Product product}) {
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context);

    //If same product already in card (Go to cart)
    if (shoppingCartQuantityDataModel.productReferenceList
        .contains(product.productReference)) {
      return AppStrings.goToCart;
    }

    //If cart is not empty (Add to cart)
    else if (shoppingCartQuantityDataModel.productReferenceList.isNotEmpty) {
      return AppStrings.addToCart;
    }

    //If cart is empty (Buy now)
    return AppStrings.buyNow;
  }

  //endregion

//region Show disclaimer bottom sheet
  void showDisclaimerBottomSheet(
      {required Product product, required Function onContinue}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: AppColors.appWhite,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topRight: Radius.circular(20), topLeft: Radius.circular(20)),
      ),
      builder: (context) {
        return DisclaimerBottomSheet(
          product: product,
          onTapShowAnyways: () {
            onContinue();
          },
        );
      },
    );
  }
  //endregion

  //region GoAddAndBuy
  void goAddAndBuy({required Product product}) {
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);

    // If product already in cart, go directly to cart without showing disclaimer
    if (shoppingCartQuantityDataModel.productReferenceList
        .contains(product.productReference)) {
      gotoShoppingCart();
      return;
    }

    // For new products being added to cart, check if there's a disclaimer
    if (product.disclaimerMessage != null &&
        product.disclaimerMessage!.isNotEmpty) {
      // Show disclaimer bottom sheet
      showDisclaimerBottomSheet(
        product: product,
        onContinue: () {
          // Continue with purchase flow after user clicks "Show Anyways"
          proceedWithAddToCart(product: product);
        },
      );
      return;
    }

    // If no disclaimer, proceed directly with adding to cart
    proceedWithAddToCart(product: product);
  }

  // Helper method to proceed with adding to cart after disclaimer check
  void proceedWithAddToCart({required Product product}) {
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);

    //If cart is not empty (Add to cart)
    if (shoppingCartQuantityDataModel.productReferenceList.isNotEmpty) {
      addToCart(product: product, goToCart: false);
      return;
    }
    //If cart is empty (Buy now)
    if (shoppingCartQuantityDataModel.productReferenceList.isEmpty) {
      addToCart(product: product, goToCart: true);
      return;
    }
  }
//endregion

  ///Single product api call
//region Single product Api Call
  Future<void> getSingleProduct({required Product product}) async {
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    try {
      //Loading
      isButtonLoadingCtrl.sink.add(true);
      //Api call
      var singleProductDetail = await SingleProductAndImageService()
          .getSingleProductInfo(
              productReference: product.productReference!,
              pinCode: AppConstants.appData.isUserView!
                  ? AppConstants.appData.pinCode!
                  : '000000');

      //Update the deliverable info
      for (var data in productDataModel.allProducts) {
        if (data.productReference == product.productReference) {
          //Update the deliverable info
          data.isBuyEnable = singleProductDetail.singleProduct!.isBuyEnable;
          //Update deliver ability
          data.deliverable = singleProductDetail.singleProduct!.deliverable;
          //Update message
          data.productStatusMessage =
              singleProductDetail.singleProduct!.productStatusMessage;
          //Update ui
          productDataModel.updateUi();
          //Make the flag isPinCodeChange to false
          data.isPinCodeChanged = false;
        }
      }
      //Update ui
      productDataModel.updateUi();

      //Loading stop
      isButtonLoadingCtrl.sink.add(false);
    } on ApiErrorResponseMessage catch (error) {
      //Loading stop
      isButtonLoadingCtrl.sink.add(false);
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      return;
    } catch (error) {
      //Loading stop
      isButtonLoadingCtrl.sink.add(false);
      return;
    }
  }
//endregion

  // region Go to Single product screen
  void goToSingleProductScreen({required String productReference}) {
    var screen =
        BuyerViewSingleProductScreen(productReference: productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
//endregion

//region Show buy options
  void showBuyOptions({required Product product}) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => BuyOptionsBottomSheet(
        onTapBuyOnSwadesic: () {
          goAddAndBuy(product: product);
        },
        onTapPickupFromStore: () {
          showPickupLocations(product: product);
        },
      ),
    );
  }
  //endregion
}
