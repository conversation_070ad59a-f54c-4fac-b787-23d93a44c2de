import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/preview/preview_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class Preview extends StatefulWidget {
  const Preview({Key? key}) : super(key: key);

  @override
  State<Preview> createState() => _PreviewState();
}

class _PreviewState extends State<Preview> {
  //region Bloc
  late PreviewBloc previewBloc;
  //endregion
  //region Init
  @override
  void initState() {
    previewBloc = PreviewBloc(context);
    previewBloc.init();
    super.initState();
  }

  //endregion
  //region Dispose
  @override
  void dispose() {
    previewBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return appIcon();
    // return StreamBuilder<bool>(
    //   stream: previewBloc.previewRefreshCtrl.stream,
    //   initialData: true,
    //   builder: (context, snapshot) {
    //     return AnimatedSwitcher(duration:  const Duration(milliseconds: 500),
    //       transitionBuilder: (Widget child, Animation<double> animation) {
    //         return FadeTransition(
    //           opacity: animation,
    //           child: child,
    //         );
    //       },
    //       child: previewBloc.isFirstWidget?previewText():appIcon(),
    //
    //     );
    //   }
    // );
  }
//endregion

//region Preview text
  Widget previewText() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Text(AppStrings.appName,style: TextStyle(
        //   fontFamily: AppConstants.rBold,
        //   fontWeight: FontWeight.w700,
        //   // fontSize: 21,
        //     fontSize:  CommonMethods.fontSizeChanger(size: 21),
        //     color: AppColors.brandGreen
        // ),),

        SizedBox(height: 24, child: Image.asset(AppImages.swadesicName)),

        horizontalSizedBox(10),
        AppToolTip(
            toolTipWidget: Container(
              padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
              decoration: BoxDecoration(
                  color: AppColors.appWhite,
                  borderRadius: BorderRadius.circular(3),
                  border: Border.all(color: AppColors.previewGreen, width: 1)),
              child: Text(
                "preview",
                style: TextStyle(
                  color: AppColors.previewGreen,
                  fontWeight: FontWeight.w700,
                  fontFamily: AppConstants.cNeuwRegular,
                  fontSize: CommonMethods.fontSizeChanger(size: 13),
                ),
              ),
              // ),message: AppStrings.thisIsSellerExclusive),
            ),
            message: AppStrings.previewSuggests),
      ],
    );
  }
//endregion

//region App Icon
  Widget appIcon() {
    return InkWell(
        onTap: () {
          var screen = CommonReferralPage();
          var route = CupertinoPageRoute(builder: (context) => screen);
          Navigator.push(
              AppConstants.userStoreCommonBottomNavigationContext, route);
        },
        child: Image.asset(
          AppImages.appIcon,
          height: 35,
          width: 35,
        ));
  }
//endregion
}
