import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/banner/banner.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access.dart';
import 'package:swadesic/features/buyers/buyer_home/preview/preview.dart';
import 'package:swadesic/features/buyers/buyer_home/visited_store_grid/visited_store.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/follower_and_supporter_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/stores_and_people/stores_and_people.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/feed/feed_screen.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/model/app_data/app_data.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class FollowerAndSupporters extends StatefulWidget {
  final String reference;

  const FollowerAndSupporters({Key? key, required this.reference}) : super(key: key);

  @override
  State<FollowerAndSupporters> createState() => _FollowerAndSupportersState();
}

class _FollowerAndSupportersState extends State<FollowerAndSupporters>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin<FollowerAndSupporters> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  // region Bloc
  late FollowerAndSupporterBloc followerAndSupporterBloc;

  //Tab controller
  late TabController followersAndSupportersTabCtrl = TabController(length: 3, vsync: this, initialIndex: 0);
  late TabController peopleAndStoreTabCtrl = TabController(length: 2, vsync: this, initialIndex: 0);
  late TabController followingAndPendingTabCtrl = TabController(length: 1, vsync: this, initialIndex: 0);

// endregion

// region Init
  @override
  void initState() {
    //print("Init");
    followerAndSupporterBloc = FollowerAndSupporterBloc(context, followersAndSupportersTabCtrl, peopleAndStoreTabCtrl);
    followerAndSupporterBloc.init();
    AppConstants.isBottomNavigationMounted.value = true;
    super.initState();
  }

//region Dispose
  @override
  void dispose() {
    imageCache.clear();
    followerAndSupporterBloc.dispose();
    super.dispose();
  }

//endregion

// endregion

// region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body: SafeArea(child: body()),
    );
  }

// endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        // title:AppStrings.addProduct,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: kIsWeb?false: widget.reference == (AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference),
        textButtonWidget: AppCommonWidgets.appBarTextButtonText(text: AppStrings.findUsingContact),
        onTapTextButton: () {
          followerAndSupporterBloc.goToFindYourCustomer();
        });
  }

  //endregion

  //region Body
  Widget body() {
    return Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.reference.startsWith("S")
            ? [
                storeFollowAndSupportingTab(),
                Flexible(child: storeTabView()),
              ]
            : [
                userFollowAndSupportingTab(),
                Flexible(child: userTabView()),
              ]);
  }

  //endregion

  ///User

  //region User follow and supporting tab Bar
  StreamBuilder<bool> userFollowAndSupportingTab() {
    return StreamBuilder<bool>(
        stream: followerAndSupporterBloc.refreshTabCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: kToolbarHeight * 0.8,
            child: TabBar(
                controller: followerAndSupporterBloc.followersAndSupportersTabCtrl,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: AppColors.appBlack,
                    // width: 0.0,
                  ),
                ),
                onTap: (index) {
                  // notificationBloc.tabRefreshCtrl.sink.add(true);
                },
                padding: EdgeInsets.zero,
                // isScrollable: true,
                tabs: [
                  //Supporters
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.followersCap,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  followerAndSupporterBloc.followersAndSupportersTabCtrl.index == 0 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Supporting
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.supporting,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  followerAndSupporterBloc.followersAndSupportersTabCtrl.index == 1 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Following
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.following,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  followerAndSupporterBloc.followersAndSupportersTabCtrl.index == 2 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                ]),
          );
        });
  }

//endregion

  //region User tab view
  TabBarView userTabView() {
    return TabBarView(
        // physics: NeverScrollableScrollPhysics(),

        controller: followerAndSupporterBloc.followersAndSupportersTabCtrl,
        // controller: TabController(length: 3, vsync: this,),
        children: [
          //Followers
          //People and store
          Column(
            children: [
              storeAndPeopleTabs(),
              Flexible(
                child: StreamBuilder<bool>(
              stream: followerAndSupporterBloc.refreshTabCtrl.stream,
                  builder: (context, snapshot) {
                    return IndexedStack(
                      index:followerAndSupporterBloc.peopleAndStoreTabCtrl.index ,
                      children: [
                        StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.USER),
                        StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.STORE),
                      ],
                    );
                  }
                ),
              ),
              // Flexible(
              //     child: TabBarView(
              //
              //         controller: followerAndSupporterBloc.peopleAndStoreTabCtrl,
              //         // controller: TabController(length: 3, vsync: this,),
              //         children: [
              //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.USER),
              //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.STORE),
              //     ]))
            ],
          ),
          //Supporting
          Column(
            children: [
              Flexible(
                child: StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.STORE),
              )
            ],
          ),
          //Following/Pending

          //Make this visible if only logged in user reference is same as the reference
          // visible: widget.reference == (AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference),
          widget.reference == (AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference)
              ? Column(
                  children: [
                    storeAndPeopleTabs(secondTabTitle: "Pending"),

                    Flexible(
                      child: StreamBuilder<bool>(
                          stream: followerAndSupporterBloc.refreshTabCtrl.stream,
                          builder: (context, snapshot) {
                            return IndexedStack(
                              index:followerAndSupporterBloc.peopleAndStoreTabCtrl.index ,
                              children: [
                                StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
                                StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.UNREGISTERED),

                              ],
                            );
                          }
                      ),
                    ),

                    // Flexible(
                    //     child: TabBarView(
                    //         controller: followerAndSupporterBloc.peopleAndStoreTabCtrl,
                    //         // controller: TabController(length: 3, vsync: this,),
                    //         children: [
                    //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
                    //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.UNREGISTERED),
                    //
                    //         ]))
                  ],
                )
              : StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
        ]);
  }

  //endregion

  ///Store
  //region Store follow and supporting tab Bar
  StreamBuilder<bool> storeFollowAndSupportingTab() {
    return StreamBuilder<bool>(
        stream: followerAndSupporterBloc.refreshTabCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: kToolbarHeight * 0.8,
            child: TabBar(
                controller: followerAndSupporterBloc.followersAndSupportersTabCtrl,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: AppColors.appBlack,
                    // width: 0.0,
                  ),
                ),
                onTap: (index) {
                  // notificationBloc.tabRefreshCtrl.sink.add(true);
                },
                padding: EdgeInsets.zero,
                // isScrollable: true,
                tabs: [
                  //Supporters
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.supporters,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  followerAndSupporterBloc.followersAndSupportersTabCtrl.index == 0 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Supporting
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.supporting,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  followerAndSupporterBloc.followersAndSupportersTabCtrl.index == 1 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Following
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.following,
                          style: AppTextStyle.settingHeading1(
                              textColor:
                                  followerAndSupporterBloc.followersAndSupportersTabCtrl.index == 2 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                ]),
          );
        });
  }

//endregion

  //region Store tab view
  TabBarView storeTabView() {
    return TabBarView(
        controller: followerAndSupporterBloc.followersAndSupportersTabCtrl,
        // controller: TabController(length: 3, vsync: this,),
        children: [
          //Followers
          //People and store
          Column(
            children: [
              storeAndPeopleTabs(),

              Flexible(
                child: StreamBuilder<bool>(
                    stream: followerAndSupporterBloc.refreshTabCtrl.stream,
                    builder: (context, snapshot) {
                      return IndexedStack(
                        index:followerAndSupporterBloc.peopleAndStoreTabCtrl.index ,
                        children: [
                          StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.USER),
                          StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.STORE),

                        ],
                      );
                    }
                ),
              ),
              // Flexible(
              //   child: IndexedStack(
              //     index:followerAndSupporterBloc.peopleAndStoreTabCtrl.index ,
              //     children: [
              //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.USER),
              //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.STORE),
              //
              //     ],
              //   ),
              // ),
              // Flexible(
              //     child: TabBarView(
              //         controller: followerAndSupporterBloc.peopleAndStoreTabCtrl,
              //         // controller: TabController(length: 3, vsync: this,),
              //         children: [
              //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.USER),
              //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWERS, entityType: EntityType.STORE),
              //     ]))
            ],
          ),
          //Supporting
          Column(
            children: [
              Flexible(
                child: StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.STORE),
              )
            ],
          ),
          //Following / pending
          //Make this visible if only logged in user reference is same as the reference
          // visible: widget.reference == (AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference),
          widget.reference == (AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference)
              ? Column(
                  children: [
                    storeAndPeopleTabs(secondTabTitle: "Pending"),

                    Flexible(
                      child: StreamBuilder<bool>(
                          stream: followerAndSupporterBloc.refreshTabCtrl.stream,
                          builder: (context, snapshot) {
                            return IndexedStack(
                              index:followerAndSupporterBloc.peopleAndStoreTabCtrl.index ,
                              children: [
                                StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
                                StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.UNREGISTERED),

                              ],
                            );
                          }
                      ),
                    ),


                    // Flexible(
                    //     child: TabBarView(
                    //         controller: followerAndSupporterBloc.peopleAndStoreTabCtrl,
                    //         // controller: TabController(length: 3, vsync: this,),
                    //         children: [
                    //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
                    //       StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.UNREGISTERED),
                    //
                    //         ]))
                  ],
                )
              : StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
        ]);
  }

  //endregion

  //region Only people tab
  Widget onlyPeopleTab() {
    return TabBarView(
        controller: followerAndSupporterBloc.peopleAndStoreTabCtrl,
        // controller: TabController(length: 3, vsync: this,),
        children: [
          StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.USER),
          //Make this visible if only logged in user reference is same as the reference
          // visible: widget.reference == (AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference),

          StoresAndPeople(reference: widget.reference, requiredList: FollowEnum.FOLLOWING, entityType: EntityType.UNREGISTERED),
        ]);
  }

  //endregion

  // region Store and people tabs
  StreamBuilder<bool> storeAndPeopleTabs({String firstTabTitle = "People", String secondTabTitle = "Stores"}) {
    return StreamBuilder<bool>(
        stream: followerAndSupporterBloc.refreshTabCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: kToolbarHeight * 0.8,
            child: TabBar(
                controller: followerAndSupporterBloc.peopleAndStoreTabCtrl,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: AppColors.appBlack,
                    // width: 0.0,
                  ),
                ),
                onTap: (index) {
                  // notificationBloc.tabRefreshCtrl.sink.add(true);
                },
                padding: EdgeInsets.zero,
                // isScrollable: true,
                tabs: [
                  //First tab
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          firstTabTitle,
                          style: AppTextStyle.settingHeading1(
                              textColor: followerAndSupporterBloc.peopleAndStoreTabCtrl.index == 0 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Secodnd tab
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          secondTabTitle,
                          style: AppTextStyle.settingHeading1(
                              textColor: followerAndSupporterBloc.peopleAndStoreTabCtrl.index == 1 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                ]),
          );
        });
  }
//endregion
}
