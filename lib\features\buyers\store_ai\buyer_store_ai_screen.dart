import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/services/store_ai_service/store_ai_service.dart';

class BuyerStoreAiScreen extends StatefulWidget {
  const BuyerStoreAiScreen({Key? key}) : super(key: key);

  @override
  State<BuyerStoreAiScreen> createState() => _BuyerStoreAiScreenState();
}

class _BuyerStoreAiScreenState extends State<BuyerStoreAiScreen> {
  bool _isLoading = true;
  bool _isRegistered = false;
  late StoreAiService _storeAiService;

  @override
  void initState() {
    super.initState();
    _storeAiService = StoreAiService();
    _checkFeatureStatus();
  }

  Future<void> _checkFeatureStatus() async {
    final status = await _storeAiService.getFeatureRequestStatus();
    setState(() {
      _isRegistered = status;
      _isLoading = false;
    });
  }

  Future<void> _registerForStoreAi() async {
    setState(() => _isLoading = true);
    final success = await _storeAiService.addFeatureRequest();
    setState(() {
      _isRegistered = success;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        isCartVisible: false,
        isMembershipVisible: false,
        isDefaultMenuVisible: false,
        backgroundColor: AppColors.appWhite,
        onTapLeading: () => Navigator.pop(context),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Meet 'Store AI'\n– The Store's Digital Mind",
                    style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 14),
                  Text(
                    "Store AI is the store itself, brought to life. It thinks, speaks, and assists like the seller, shaped by the store's knowledge, products, and interactions. Forget support chat bots—this one actually knows what it's talking about.",
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 14),
                ],
              ),
            ),
            Image.asset(
              AppImages.storeAiInfoImage,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 18),
                  Text(
                    "What Store AI Does for You",
                    style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 18),
                  _buildFeatureItem(
                    "Talk to the Store, Anytime",
                    "Ask about products, orders, tracking, and more—Store AI responds instantly.",
                  ),
                  _buildFeatureItem(
                    "Handles Your Queries",
                    "Store AI replies to your questions, comments, and reviews, making shopping effortless.",
                  ),
                  _buildFeatureItem(
                    "Knows Everything About the Store",
                    "Get detailed specs, unbiased insights, and recommendations straight from the source.",
                  ),
                  _buildFeatureItem(
                    "No Sales Pressure",
                    "It helps you decide without the typical sales pitch.",
                  ),
                  _buildFeatureItem(
                    "Problem? Sorted",
                    "Need help? Store AI resolves common issues and escalates to seller to contact you when needed.",
                  ),
                  _buildFeatureItem(
                    "Always Improving",
                    "The more the store updates, the smarter Store AI gets.",
                  ),
                  const SizedBox(height: 150),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading || _isRegistered ? null : _registerForStoreAi,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.appBlack,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14),
                        ),
                        disabledBackgroundColor: AppColors.textFieldFill2,
                      ),
                      child: Text(
                        _isLoading 
                          ? "Loading..." 
                          : _isRegistered 
                            ? "Requested"
                            : "Try it Before Others",
                        style: AppTextStyle.access0(
                          textColor: _isRegistered ? AppColors.disableBlack : AppColors.appWhite
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 75),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10, left: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "• ",
                    style: AppTextStyle.heading4SemiBold(
                      textColor: AppColors.appBlack,
                    ).copyWith(
                      fontSize: 20.0,
                    ),
                  ),
                  TextSpan(
                    text: title,
                    style: AppTextStyle.heading4SemiBold(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              description,
              style: AppTextStyle.settingText(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }
}
