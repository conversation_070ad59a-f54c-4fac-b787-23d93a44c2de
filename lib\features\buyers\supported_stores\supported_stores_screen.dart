import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_bloc.dart';
import 'package:swadesic/features/providers/store_info_provider/store_info_provider.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Store You support screen
class SupportedStoresScreen extends StatefulWidget {
  const SupportedStoresScreen({Key? key}) : super(key: key);

  @override
  _SupportedStoresScreenState createState() => _SupportedStoresScreenState();
}
// endregion

class _SupportedStoresScreenState extends State<SupportedStoresScreen> {
  //Width
  double width = 0.0;

  // region Bloc
  late SupportedStoresBloc supportedStoresBloc;

  // endregion

  // region Init
  @override
  void initState() {
    //print(context);
    supportedStoresBloc = SupportedStoresBloc(context);
    supportedStoresBloc.init();
    super.initState();
  }

  // endregion

  @override
  void dispose() {
    //print("disposed");
    super.dispose();
  }

  // region build
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, boxConstraints) {
        width = boxConstraints.maxWidth;
        return GestureDetector(
          onTap: () {
            CommonMethods.closeKeyboard(context);
          },
          child: Scaffold(
            backgroundColor: AppColors.appWhite,
            appBar: appBar(),
            body: SafeArea(child: Center(child: body())),
            // body: SafeArea(child: Center(child: supportedStores())),
          ),
        );
      },
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        isCustomTitle: false,
        title: AppStrings.supportedStores,
        isDefaultMenuVisible: true,
        isCartVisible: false,
        isMembershipVisible: true,
        onTapDrawer: () {
          // buyerViewStoreBloc.goToSellerAccountScreen();
        });
  }
  //endregion

  // region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        supportedStoresBloc.init();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15,
        ),
        child: Column(
          children: [
            searchBar(),
            verticalSizedBox(20),
            followedStores(),
            //exploreStores(),
          ],
        ),
      ),
    );
  }

// endregion

//region Followed Stores
  Widget followedStores() {
    return Expanded(
      child: StreamBuilder<SupportedStoresState>(
          stream: supportedStoresBloc.storeYouFollowedCtrl.stream,
          initialData: SupportedStoresState.Loading,
          builder: (context, snapshot) {
            //print(snapshot.data);
            if (snapshot.data == SupportedStoresState.Loading) {
              return Center(child: AppCommonWidgets.appCircularProgress());
              // return const Center(child: CircularProgressIndicator(),);
            }
            if (snapshot.data == SupportedStoresState.SearchEmpty) {
              return Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.width,
                child: const NoResult(
                    message: AppStrings.noMatchingResultInSupported),
              );

              return AppCommonWidgets.emptyResponseText(
                  emptyMessage: AppStrings.noMatchingResultInSupported);
              // return const Center(child: CircularProgressIndicator(),);
            }

            if (snapshot.data == SupportedStoresState.Empty) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  emptyImage(),
                  verticalSizedBox(30),
                  youAreNotFollowing(),
                  verticalSizedBox(150),
                  //exploreStores()
                ],
              );
            }
            if (snapshot.data == SupportedStoresState.Success) {
              var data = supportedStoresBloc.storeList;
              return GridView.builder(
                shrinkWrap: true,
                //itemCount: buyerHomeBloc.recentlyVisitedItemCount,
                itemCount: data.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.5),

                  /// crossAxisSpacing: 15,
                  // childAspectRatio: 0.7
                  crossAxisCount: 4,

                  /// childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.5),
                  mainAxisSpacing: 20,

                  /// crossAxisSpacing: 15,
                  // childAspectRatio: 0.7
                  crossAxisSpacing: 15,

                  mainAxisExtent: width / 5 +
                      CommonMethods.textHeight(
                        textStyle: AppTextStyle.subTitle(
                            textColor: AppColors.appBlack),
                        context: context,
                      ) +
                      6,

                  // crossAxisCount: 4,
                  // // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.5),
                  // mainAxisSpacing: 17,
                  // crossAxisSpacing: 15,
                  // mainAxisExtent: 100,
                ),
                // physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return AppCommonWidgets.storeCardGreed(
                    width: width,
                    context: context,
                    onTap: () {
                      supportedStoresBloc.goToBuyerViewStore(data[index]);
                    },
                    storeInfo: data[index],
                  );
                },
              );
            }
            return Center(child: AppCommonWidgets.errorWidget(onTap: () {
              supportedStoresBloc.init();
            }));
          }),
    );
  }
//endregion

  //region Searchbar
  Widget searchBar() {
    return AppSearchField(
      textEditingController: supportedStoresBloc.searchTextCtrl,
      onChangeText: (v) {
        supportedStoresBloc.onChangeSearchField();
      },
      onTapSuffix: () {
        supportedStoresBloc.onChangeSearchField();
      },
      hintText: AppStrings.searchSupportedStore,
    );
  }
  //endregion

//region Empty image
  Widget emptyImage() {
    return Center(
        child: SvgPicture.asset(
      AppImages.storesYouFollow,
      fit: BoxFit.cover,
    ));
  }
//endregion

//region You Are not Following
  Widget youAreNotFollowing() {
    return Text(
      AppStrings.youAreNotFollowing,
      textAlign: TextAlign.center,
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
//endregion

//region Explore stores
  Widget exploreStores() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        //supportedStoresBloc.goToStoreYouFollow();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 15,
        ),
        decoration: BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: const BorderRadius.all(Radius.circular(100)),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 5,
              color: AppColors.appBlack.withOpacity(0.2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            AppStrings.exploreStores,
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w700,
                fontFamily: "LatoSemiBold",
                color: AppColors.appWhite),
          ),
        ),
      ),
    );
  }
//endregion

  Widget supportedStores() {
    return Consumer<StoreInfoProvider>(
      builder: (context, storeData, _) {
        //print("Supported store state is ${storeData.supportedState}");
        //Get filter supported stores
        final supportedStores = storeData.storeInfoList
            .where((store) =>
                store.followStatus!.toLowerCase() ==
                FollowEnum.SUPPORTING.name.toLowerCase())
            .toList();

        // final supportedStores = storeData.getFilteredSupportedStores();
        //Failed
        if (storeData.supportedState == AppState.Failed) {
          return AppCommonWidgets.errorWidget(onTap: () {
            storeData.getSupportedStores();
          });
        }
        //Loading
        if (storeData.supportedState == AppState.Loading) {
          return Center(child: AppCommonWidgets.appCircularProgress());
        }
        //Empty
        if (supportedStores.isEmpty) {
          return NoResult(
            message: AppStrings.noResults,
          );
        }
        return ListView.builder(
          itemCount: supportedStores.length,
          itemBuilder: (context, index) {
            // final store = storeData.seenStores[index];
            return ListTile(
              title: Text(supportedStores[index].storeName!),
              trailing: IconButton(
                icon: Icon(supportedStores[index].isLike
                    ? Icons.favorite
                    : Icons.favorite_border),
                onPressed: () =>
                    storeData.toggleLike(storeInfo: supportedStores[index]),
              ),
            );
          },
        );
      },
    );
  }
}
