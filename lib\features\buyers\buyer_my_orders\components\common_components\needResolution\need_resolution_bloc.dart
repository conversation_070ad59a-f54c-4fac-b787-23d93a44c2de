import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class NeedResolutionBloc {
  // region Common Variables
  BuildContext context;
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  late BuyerMyOrderServices buyerMyOrderServices;
  final String escalationReason;
  final String? packageNumber;


  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final notesTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  NeedResolutionBloc(this.context, this.subOrderList, this.buyerSubOrderBloc, this.order,this.escalationReason, this.packageNumber,);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
  }

// endregion

  //region Add need resolution
  addNeedResolution() async {


    //Check text field is empty or not
    if(notesTextCtrl.text.isEmpty){
      return CommonMethods.toastMessage(AppStrings.notesCanNotBeEmpty, context);
    }
    //Sub order number list
    List<String> subOrderNumberList = [];
    for(var data  in subOrderList){
      subOrderNumberList.add(data.suborderNumber!);
    }

    //region Try
    try {
      await buyerMyOrderServices.buyerNeedResolution(
          orderNumber: order.orderNumber!,
          escalationReason:escalationReason,
          notes: notesTextCtrl.text,
          subOrderNumberList: subOrderNumberList.join("|"),
          subOrderStatus: subOrderList.first.suborderStatus!,
          packageNumber: packageNumber
       );

      //Close bottom sheet
      Navigator.pop(context);
      //Message
      CommonMethods.toastMessage(AppStrings.escalationSuccessful, context);
      // CommonMethods.snackBar("${selectedSubOrders.length==1?"${selectedSubOrders.length} ${AppStrings.order}":"${selectedSubOrders.length} ${AppStrings.orders}"} ${AppStrings.confirmed}", context);
      //Call the get sub order api
      buyerSubOrderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion

}
