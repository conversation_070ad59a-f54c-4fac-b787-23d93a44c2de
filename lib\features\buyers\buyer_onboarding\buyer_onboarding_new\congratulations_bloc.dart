import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class CongratulationsBloc {
  // region Common Methods
  BuildContext context;
  final String userReference;
  final Map<String, dynamic> userData;
  final String? profilePicturePath;

  late UserDetailsServices userDetailsServices;
  static GetUserDetailsResponse userDetailsResponse = GetUserDetailsResponse();

  // endregion

  //region Controller
  final loadingCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  CongratulationsBloc(
      this.context, this.userReference, this.userData, this.profilePicturePath);

  // endregion

  // region Init
  void init() {
    userDetailsServices = UserDetailsServices();
  }
  // endregion

  //region Continue To Home
  void continueToHome() async {
    loadingCtrl.sink.add(true);

    try {
      // Create user profile
      await createUserProfile();

      // Upload profile picture if available
      if (profilePicturePath != null) {
        await uploadProfilePicture();
      }

      // Get user details
      await getLoggedInUserDetail();

      // Navigate to home
      goToBottomNavigation();
    } catch (error) {
      loadingCtrl.sink.add(false);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  //endregion

  //region Create User Profile
  Future<void> createUserProfile() async {
    try {
      // Update user info api
      await userDetailsServices.editUserProfile(
          data: userData, userReference: userReference);
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      rethrow;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      rethrow;
    }
  }
  //endregion

  //region Upload Profile Picture
  Future<void> uploadProfilePicture() async {
    try {
      // Create a custom implementation to pass the user reference explicitly
      await _addUserProfilePicWithUserReference(
        fileNameWithExtension: profilePicturePath.toString(),
        filePath: profilePicturePath!,
        userReference: userReference,
      );
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      // Don't throw error here, as profile picture upload is optional
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      // Don't throw error here, as profile picture upload is optional
    }
  }

  // Custom implementation to pass user reference explicitly
  Future<void> _addUserProfilePicWithUserReference({
    required String fileNameWithExtension,
    required String filePath,
    required String userReference,
  }) async {
    var dio = Dio();

    // Get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    // Generate body
    var body = {
      "icon": file,
    };

    // Create form data
    FormData formData = FormData.fromMap(body);

    // Url with explicit user reference
    var url = "${AppConstants.updateUserProfilePic}$userReference/";

    // Headers
    var headers = {
      'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
      'Content-Type': 'multipart/form-data',
    };

    // Start upload file
    await dio
        .patch(
          url,
          options: Options(headers: headers),
          data: formData,
        )
        .timeout(const Duration(seconds: 400));
  }
  //endregion

  //region Get logged in user detail
  Future<void> getLoggedInUserDetail() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);

    try {
      userDetailsResponse = await UserDetailsServices()
          .getLoggedInUserDetail(userReference: userReference);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: userDetailsResponse.userDetail!);

      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }

      //Update ui
      productDataModel.updateUi();

      ///Save user info to global and share pref
      saveUserInfoInGlobalAndSharePref();
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      debugPrint(error.message);
      rethrow;
    } catch (error) {
      debugPrint(error.toString());
      rethrow;
    }
  }
  //endregion

  ///Save user info in global variable and in share preference
  //region Save info in global variable and share preference
  void saveUserInfoInGlobalAndSharePref() {
    //Add user id
    AppConstants.appData.userId = userDetailsResponse.userDetail!.userid!;
    //Add user reference
    AppConstants.appData.userReference =
        userDetailsResponse.userDetail!.userReference!;
    //Mark user view is true
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Pin code
    AppConstants.appData.pinCode = userDetailsResponse.userDetail!.pincode;
    //Add all data to share pref
    AppDataService().addAppData();
  }
  //endregion

  //region Go to Bottom Navigation
  void goToBottomNavigation() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const UserBottomNavigation()),
      (Route<dynamic> route) => false,
    );
  }
  //endregion

  //region Dispose
  void dispose() {
    loadingCtrl.close();
  }
  //endregion
}
