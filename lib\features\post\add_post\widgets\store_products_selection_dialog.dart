import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class StoreProductsSelectionDialog extends StatefulWidget {
  final SuggestionItem storeItem;
  final Function(SuggestionItem) onProductSelected;

  const StoreProductsSelectionDialog({
    super.key,
    required this.storeItem,
    required this.onProductSelected,
  });

  @override
  State<StoreProductsSelectionDialog> createState() =>
      _StoreProductsSelectionDialogState();
}

class _StoreProductsSelectionDialogState
    extends State<StoreProductsSelectionDialog> {
  final TypingSuggestionsService _service = TypingSuggestionsService();
  List<SuggestionItem> products = [];
  bool isLoading = true;
  bool hasMore = false;
  int offset = 0;
  final int limit = 10;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  Future<void> _loadProducts({bool loadMore = false}) async {
    if (!loadMore) {
      setState(() {
        isLoading = true;
        offset = 0;
        products.clear();
      });
    }

    try {
      String query = "@${widget.storeItem.primaryText}/";
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      TypingSuggestionsResponse response = await _service.getTypingSuggestions(
        query: query,
        limit: limit,
        offset: offset,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      setState(() {
        if (loadMore) {
          products.addAll(response.results ?? []);
        } else {
          products = response.results ?? [];
        }
        hasMore = (response.results?.length ?? 0) == limit;
        offset += limit;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        CommonMethods.toastMessage('Failed to load products', context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Product',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(
                    Icons.close,
                    color: AppColors.writingBlack1,
                  ),
                ),
              ],
            ),

            // Store info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  CustomImageContainer(
                    width: 30,
                    height: 30,
                    imageUrl: widget.storeItem.imageUrl,
                    imageType: CustomImageContainerType.store,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.storeItem.primaryText ?? '',
                    style: AppTextStyle.contentText0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Products list
            Expanded(
              child: _buildProductsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    if (isLoading && products.isEmpty) {
      return AppCommonWidgets.appCircularProgress();
    }

    if (products.isEmpty) {
      return Center(
        child: Text(
          'No products found',
          style: AppTextStyle.contentText0(
            textColor: AppColors.writingBlack1,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: products.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < products.length) {
          return _buildProductItem(products[index]);
        } else {
          return _buildLoadMoreItem();
        }
      },
    );
  }

  Widget _buildProductItem(SuggestionItem product) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        widget.onProductSelected(product);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            CustomImageContainer(
              width: 40,
              height: 40,
              imageUrl: product.imageUrl,
              imageType: CustomImageContainerType.product,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (product.secondaryText != null)
                    Text(
                      product.secondaryText!,
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreItem() {
    return InkWell(
      onTap: () => _loadProducts(loadMore: true),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: isLoading
              ? AppCommonWidgets.appCircularProgress(isPaginationProgress: true)
              : Text(
                  'Load more...',
                  style: AppTextStyle.contentText0(
                    textColor: AppColors.brandBlack,
                  ),
                ),
        ),
      ),
    );
  }
}
