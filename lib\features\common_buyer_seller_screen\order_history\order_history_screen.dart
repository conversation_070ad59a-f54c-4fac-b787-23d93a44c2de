import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_history/order_history_bloc.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
//region OrderHistoryScreen
class OrderHistoryScreen extends StatefulWidget {
  final List<SubOrder> subOrderList;
  const OrderHistoryScreen({Key? key, required this.subOrderList}) : super(key: key);

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}
//endregion


class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  //region Bloc
  late OrderHistoryBloc orderHistoryBloc;
  //endregion
  //region Init
  @override
  void initState() {
    orderHistoryBloc = OrderHistoryBloc(context);
    orderHistoryBloc.init();
    super.initState();
  }
  //endregion
  //region Dispose
  @override
  void dispose() {
    orderHistoryBloc.dispose();
    super.dispose();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body:body() ,

    );
  }


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.orderHistory,
        isCartVisible:false,
        isMembershipVisible:true,
    );
  }

  //endregion

//region Body
Widget body(){
    return SingleChildScrollView(
      child: Column(
        children: [
          tapOnTheProduct(),
          subOrderList(),
          AppCommonWidgets.bottomListSpace(context: context),

        ],
      ),
    );
}
//endregion

//region Tap on the product
Widget tapOnTheProduct(){
    return Container(
      width: double.infinity,
      color: AppColors.lightGray2,
      padding: const EdgeInsets.all(10),
      child: appText(AppStrings.tapOnTheProduct,fontFamily: AppConstants.rRegular,style:FontStyle.italic,
      fontSize: 14,fontWeight: FontWeight.w400,color: AppColors.writingColor2,height: 1.19,
      )
    );
}
//endregion


//region SubOrder List
Widget subOrderList(){
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
        itemCount:widget.subOrderList.length,
        itemBuilder:(context,index){
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          padding: const EdgeInsets.symmetric(horizontal: 7),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: AppColors.textFieldFill1// Replace with your desired border width
              ),
            ),
          ),
          child: AppCommonWidgets.subOrderInfo(subOrder:widget.subOrderList[index],
              onTap: (){
                orderHistoryBloc.goToProductOrder(subOrder: widget.subOrderList[index]);            }, context: context,
              isCheckBoxVisible: false,
              isPriceDetailVisible: false,
              isStatusVisible: false,
              isArrowVisible: true

          ),
        );
      // return OrderHistoryCommonWidgets.subOrder(subOrder: widget.subOrderList[index], context: context, onTapCard: (){
      //   orderHistoryBloc.goToProductOrder(subOrder: widget.subOrderList[index]);
      // },
      //    );
    });
}
//endregion


}
