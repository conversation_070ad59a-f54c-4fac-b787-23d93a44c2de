import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class ProductCommonWidgets{

  //region Button names
  static String buttonNames({
    required Product product,
    required ShoppingCartQuantityDataModel shoppingCartQuantityDataModel
  }) {
    //print("ProductCommonWidgets - Product Reference: ${product.productReference}");
    //print("ProductCommonWidgets - Fulfillment Options: ${product.fulfillmentOptions}");
    
    if (product.fulfillmentOptions == "IN_STORE_PICKUP") {
      //print("ProductCommonWidgets - Using IN_STORE_PICKUP button text");
      return "Buy At Store";
    }

    //If nothing in cart
    if (shoppingCartQuantityDataModel.productReferenceList.isEmpty) {
      //print("ProductCommonWidgets - Cart empty, showing Buy Now");
      return AppStrings.buyNow;
    }
    //If product already in cart
    if (shoppingCartQuantityDataModel.productReferenceList.contains(product.productReference)) {
      //print("ProductCommonWidgets - Product in cart, showing Go to Cart");
      return AppStrings.goToCart;
    }
    //If cart is not empty
    if (shoppingCartQuantityDataModel.productReferenceList.isNotEmpty) {
      //print("ProductCommonWidgets - Cart not empty, showing Add to Cart");
      return AppStrings.addToCart;
    }
    return "";
  }
  //endregion


  //region Buy,Go to and update button
  static Widget buyAddAndGoToButton({
    required Product product,
    required Function onTapBuy,
    required BuildContext context,
  }){
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel = Provider.of<ShoppingCartQuantityDataModel>(context);


    return TextButton(
      onPressed: () {
        onTapBuy();
      },
      child: Text(
          buttonNames(
            product: product,
            shoppingCartQuantityDataModel: shoppingCartQuantityDataModel,
          ),
        style: AppTextStyle.access0(textColor: AppColors.appWhite)
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.appBlack, // Button background color
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.03), // Rounded edges
        ),
        padding: EdgeInsets.symmetric(vertical: kIsWeb ? 20 : 10),
        minimumSize: const Size(double.infinity, 0), // Full width button
      ),
    );



    // return  CupertinoButton(
    //     borderRadius: BorderRadius.circular(30),
    //     color: buttonColor,
    //     padding: EdgeInsets.zero,
    //     onPressed: onTap,
    //     child: Container(
    //       alignment: Alignment.center,
    //       width: double.infinity,
    //       decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(30))),
    //
    //       padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 10),
    //
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.end,
    //         crossAxisAlignment: CrossAxisAlignment.end,
    //         children: [
    //           Text(
    //               buttonName,
    //               textAlign: TextAlign.end,
    //
    //               style:AppTextStyle.access0(textColor:textColor).copyWith(height: 1)
    //           ),
    //           //Selling price
    //           Container(
    //               margin: const EdgeInsets.only(left: 3),
    //
    //               child: Text("@ ₹${product.sellingPrice}",
    //                 textAlign: TextAlign.end,
    //                 style: AppTextStyle.access0(textColor:textColor).copyWith(height: 1),)),
    //           //MRP price
    //           Visibility(
    //             visible: product.sellingPrice != product.mrpPrice,
    //             child: Container(
    //                 margin: const EdgeInsets.only(left: 10),
    //                 child: Text("₹${product.mrpPrice}",
    //                   textAlign: TextAlign.end,
    //
    //                   style: AppTextStyle.smallTextRegular(textColor:textColor,isLineThrough: true).copyWith(height: 1),)),
    //           ),
    //
    //         ],
    //       ),
    //     )
    // );
  }
//endregion

  //region Refresh to update
  static Widget refreshToUpdate({
    required Product product,
    required Function onTapBuy,
    required BuildContext context,
  }){
    return TextButton(
      onPressed: () {
        onTapBuy();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.textFieldFill1, // Button background color
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.03), // Rounded edges
        ),
        padding: EdgeInsets.symmetric(vertical: kIsWeb ? 20 : 10),
        minimumSize: const Size(double.infinity, 0), // Full width button
      ),
      child: Text(
        AppStrings.refreshForUpdate,
          style: AppTextStyle.access0(textColor: AppColors.orange)
      ),
    );

  }
//endregion

  //region Status message button
  static Widget statusMessageButton({
    required Product product,
    required BuildContext context,
  }){
    return TextButton(
      onPressed: () {
       CommonMethods.toastMessage(product.productStatusMessage!, context);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.textFieldFill1, // Button background color
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.03), // Rounded edges
        ),
        padding: EdgeInsets.symmetric(vertical: kIsWeb ? 20 : 10),
        minimumSize: const Size(double.infinity, 0), // Full width button
      ),
      child: Text(
          product.productStatusMessage!,
          style: AppTextStyle.access0(textColor: AppColors.orange)
      ),
    );

  }
//endregion

  //region Status message button
  static Widget deletedProductButton({
    required Product product,
    required BuildContext context,
  }){
    return TextButton(
      onPressed: () {
        CommonMethods.toastMessage(AppStrings.thisProductIsNoLongerAvailable, context);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.textFieldFill1, // Button background color
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.03), // Rounded edges
        ),
        padding: EdgeInsets.symmetric(vertical: kIsWeb ? 20 : 10),
        minimumSize: const Size(double.infinity, 0), // Full width button
      ),
      child: Text(
          AppStrings.thisProductIsNoLongerAvailable,
          style: AppTextStyle.access0(textColor: AppColors.orange)
      ),
    );

  }
//endregion

  //region Seller buy and update stock
    static Widget sellerBuyAndUpdateStock({
      required Product product,
      required BuildContext context,
      required Function onTap,
      required String message
    }){
      return TextButton(
        onPressed: () {
          onTap();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.textFieldFill1, // Button background color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.03), // Rounded edges
          ),
          padding: EdgeInsets.symmetric(vertical: kIsWeb ? 20 : 10),
          minimumSize: const Size(double.infinity, 0), // Full width button
        ),
        child: Text(
            message,
            style: AppTextStyle.access0(textColor: AppColors.appBlack)
        ),
      );

    }
  //endregion

}