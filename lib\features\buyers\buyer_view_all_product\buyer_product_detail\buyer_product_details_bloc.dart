import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability_locations.dart';
import 'package:swadesic/features/common_buyer_seller_screen/contact_info/contact_info.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/get_buyer_single_product_and_image/get_single_product_detail_response.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/product_comment_services/product_comment_services.dart';
import 'package:swadesic/services/single_product_and_image_service/single_product_and_image_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class BuyerProductDetailBloc {
  // region Common Variables
  BuildContext context;
  bool isDropdownActive = true;
  bool isAskSellerVisible = false;

  //Product comment
  late ProductCommentServices productCommentServices;
  final Product productFromPrevious;
   Product product = Product();
  final String? orderNumber;

  ///Store service and model
  late SingleStoreInfoServices singleStoreInfoServices;
  late SingleStoreInfoResponse singleStoreInfoResponse = SingleStoreInfoResponse();

  ///Get Product
  late SingleProductAndImageService singleProductAndImageService;
  late GetSingleProductDetailResponse getSingleProductDetailResponse;

  // endregion

  //region Text editing controller
  final askAboutTextCtrl = TextEditingController();

  //endregion

  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  final productDetailLoadingCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  BuyerProductDetailBloc(this.context, this.productFromPrevious, this.orderNumber);

  // endregion

  // region Init
  void init() {
    productCommentServices = ProductCommentServices();
    singleStoreInfoServices = SingleStoreInfoServices();
    //Get single product only if

    product.copyFrom(productFromPrevious);

    getSingleProduct();
  }

// endregion

  ///Single product api call
//region Single product Api Call
  getSingleProduct() async {
    try {

      //Call api and get response
      //If productVersion by is null
      if(productFromPrevious.productVersion == null){
        //Loading
        productDetailLoadingCtrl.sink.add(true);
        var data = await SingleProductAndImageService().getSingleProductInfo(productReference: productFromPrevious.productReference!, pinCode: '110068',);
        product.copyFrom(data.singleProduct!);
      }
      else{
        await Future.delayed(Duration.zero);
        //Loading stop
        productDetailLoadingCtrl.sink.add(false);
      }
      //If version is not empty then sub order number is there
      // else{
      //   getSingleProductDetailResponse =
      //   await singleProductAndImageService.getSingleProductInfoFromOrder(subOrderNumber: subOrderNumber);
      // }

      //Loading stop
      productDetailLoadingCtrl.sink.add(false);

    }  on ApiErrorResponseMessage catch (error) {
      //Loading stop
      productDetailLoadingCtrl.sink.add(false);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      // return;
      //
      // CommonMethods.toastMessage(AppStrings.error, context);
      // return;
    } catch (error) {
      //Loading stop
      productDetailLoadingCtrl.sink.add(false);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      // return;
    }
  }

//endregion

//region On tap dropdown
  void onTapDropDown() {
    isDropdownActive = !isDropdownActive;

    refreshCtrl.sink.add(true);
  }

  //endregion

//region Go back to Add Product Preview
  void goBack() {
    Navigator.pop(context);
  }

//endregion

  //region Go to Seller profile
  void goToProfile() {
    var screen = UserProfileScreen(
      userReference: AppConstants.appData.userReference!
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      init();
    });
  }

//endregion

  //region Get Store Info Api call
  getSingleStoreInfo() async {
    try {
      singleStoreInfoResponse = await singleStoreInfoServices.getSingleStoreInfo(productFromPrevious.storeReference!);

      //If trust center is not created and seller is viewing it's own product the show the message (Creator)
      if(singleStoreInfoResponse.data!.storeDetails!.first.phoneNumber!.isEmpty  && singleStoreInfoResponse.data!.storeDetails!.first.email!.isEmpty && AppConstants.appData.isStoreView! && AppConstants.appData.storeReference == singleStoreInfoResponse.data!.storeReference){
        return context.mounted?CommonMethods.toastMessage(AppStrings.noContactDetailsAvailablePleaseAddInTheTrustCenter, context):null;
      }

      //Check if static user then open login screen
      if(CommonMethods().isStaticUser()){
        CommonMethods().goToSignUpFlow();
        return;
      }

      // Navigate to message screen
      goToMessage();

    } on ApiErrorResponseMessage catch (error) {
      context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
    } catch (error) {
      context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
    }
  }
  //endregion

  // region Go to Message
  void goToMessage() {
    var screen = MessageDetailScreen(toEntityReference: productFromPrevious.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  //region Add Parent Comment
  addParentCommentApiCall({required String productReference}) async {
    ///Close Keybord
    CommonMethods.closeKeyboard(context);

    ///Comment Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.addComment! != "1") {
      ///Clear text field
      askAboutTextCtrl.clear();
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }
    //if Field is empty
    if (askAboutTextCtrl.text.isEmpty) {
      return CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
    }
    try {
      await productCommentServices.addParentComment(productReference, askAboutTextCtrl.text, "comment");

      ///Clear text field
      askAboutTextCtrl.clear();
    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }
//endregion





}
