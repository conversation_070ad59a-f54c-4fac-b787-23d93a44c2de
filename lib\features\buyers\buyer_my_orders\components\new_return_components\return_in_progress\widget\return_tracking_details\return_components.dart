import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_tracking_details/return_tracking_details_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnSeller extends StatelessWidget {
  final ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  const ReturnSeller({Key? key, required this.returnTrackingDetailsBloc})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Self Return",
          style: TextStyle(
            color: AppColors.appBlack,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.name,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: AppTextFields.allTextField(
            context: context,
            maxEntry: 50,
            textEditingController: returnTrackingDetailsBloc.nameTextCtrl,
            hintText: AppStrings.name,
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.phoneNumber,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: AppTextFields.mobileNumberTextField(
            context: context,
            maxEntry: 10,
            textEditingController:
                returnTrackingDetailsBloc.phoneNumberTextCtrl,
            hintText: AppStrings.phoneNumber,
          ),
        ),
      ],
    );
  }
}

class ReturnLogistic extends StatelessWidget {
  final ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  const ReturnLogistic({Key? key, required this.returnTrackingDetailsBloc})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Return by Logistics Partner",
          style: TextStyle(
            color: AppColors.appBlack,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.logisticsPartner,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: InkWell(
            onTap: () {
              returnTrackingDetailsBloc.onTapLogisticsPartners();
            },
            child: AppTextFields.allTextField(
              context: context,
              maxEntry: 50,
              textEditingController:
                  returnTrackingDetailsBloc.logisticPartnerTextCtrl,
              hintText: AppStrings.logisticsPartner,
              enabled: false,
            ),
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.trackingNumber,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: AppTextFields.allTextField(
            context: context,
            maxEntry: 50,
            textEditingController:
                returnTrackingDetailsBloc.trackingNumberTextCtrl,
            hintText: AppStrings.trackingNumber,
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.trackingLink,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: AppTextFields.allTextField(
            context: context,
            maxEntry: 200,
            textEditingController:
                returnTrackingDetailsBloc.trackingLinkTextCtrl,
            hintText: AppStrings.trackingLink,
            onChanged: (value) {
              returnTrackingDetailsBloc.validateUrl(value);
            },
          ),
        ),

        ///If url is in-valid
        returnTrackingDetailsBloc.isUrlValid != null &&
                !returnTrackingDetailsBloc.isUrlValid!
            ? AppCommonWidgets.validAndInvalid(
                buttonText: AppStrings.invalidUrl,
                textColor: AppColors.red,
              )
            : const SizedBox(),

        ///If url is valid
        Visibility(
          visible: returnTrackingDetailsBloc.isUrlValid != null &&
              returnTrackingDetailsBloc.isUrlValid!,
          child: AppCommonWidgets.validAndInvalid(
              buttonText: AppStrings.viewTheLink,
              textColor: AppColors.brandBlack,
              isUnderLine: true,
              onTap: () {
                CommonMethods.opeAppWebView(
                    context: context,
                    webUrl:
                        returnTrackingDetailsBloc.trackingLinkTextCtrl.text);
              }),
        ),
      ],
    );
  }
}
