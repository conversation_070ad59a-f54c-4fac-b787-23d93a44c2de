import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/add_image_option/add_image_option_screen.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/add_post/widgets/store_mention_options_dialog.dart';
import 'package:swadesic/features/post/add_post/widgets/store_products_selection_dialog.dart';
import 'package:swadesic/features/post/add_post/widgets/typing_suggestions_overlay.dart';
import 'package:swadesic/features/post/add_post/widgets/enhanced_typing_suggestions_overlay.dart';
import 'package:swadesic/features/post/upload_post_files_in_background/upload_post_files.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/my_reach_text_controller/my_reach_text_controller.dart';
import 'package:swadesic/util/web_file_picker.dart';

enum AddPostScreenState { Loading, Success, Failed, Empty }

class AddPostScreenBloc {
  //region Common variable
  late BuildContext context;
  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel;

  // For mobile platform
  List<File> selectedImage = [];

  // For web platform
  List<Map<String, dynamic>> webImages = [];

  // Typing suggestions
  final TypingSuggestionsService _typingSuggestionsService =
      TypingSuggestionsService();
  List<SuggestionItem> suggestions = [];
  bool isLoadingSuggestions = false;
  bool showSuggestions = false;
  String currentQuery = '';
  int suggestionsOffset = 0;
  final int suggestionsLimit = 10;
  bool hasMoreSuggestions = false;

  // Mention data for post creation
  List<MentionData> mentions = [];

  // Tagged items from access settings (not shown in text field)
  List<SuggestionItem> taggedItemsFromAccess = [];
  String selectedTagTab = 'PRODUCT'; // Default to PRODUCT tab
  //endregion

//region Text Editing Controller
//   final TextEditingController addPostTextCtrl = TextEditingController();
//endregion

//region Controller
  final addUpdatePostScreenStateCtrl =
      StreamController<AddPostScreenState>.broadcast();
  final selectedImageCtrl = StreamController<List<File>>.broadcast();
  final webImagesCtrl =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final suggestionsCtrl = StreamController<List<SuggestionItem>>.broadcast();
  final showSuggestionsCtrl = StreamController<bool>.broadcast();
  final suggestionsLoadingCtrl = StreamController<bool>.broadcast();
  final taggedItemsFromAccessCtrl = StreamController<List<SuggestionItem>>.broadcast();
  final selectedTagTabCtrl = StreamController<String>.broadcast();
//endregion

  //region Text editing ctrl
  MyReachTextController addPostTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(AppConstants.atTag):
        AppTextStyle.contentText0(textColor: AppColors.brandGreen),
  }, onMatch: (List<String> match) {});
  //endregion
  //region Constructor
  AddPostScreenBloc(this.context);
  //endregion
//region Init
  void init() {
    appConfigDataModel =
        Provider.of<AppConfigDataModel>(context, listen: false);

    // Add text change listener for typing suggestions
    addPostTextCtrl.addListener(_onTextChanged);

    // Initialize selected tab stream
    selectedTagTabCtrl.sink.add(selectedTagTab);
  }
//endregion

//region On tap add image
  Future<void> onTapAddImage() async {
    int imageLimit = appConfigDataModel.appConfig!.postImageLimit;

    // For web platform
    if (kIsWeb) {
      // Check if image count has reached the limit
      int totalImages = webImages.length;
      if (totalImages >= imageLimit) {
        // Store context in a local variable to avoid BuildContext across async gaps warning
        final currentContext = context;
        if (currentContext.mounted) {
          return CommonMethods.toastMessage(
              "${AppStrings.youCantAddMoreThen} $imageLimit images",
              currentContext);
        }
        return;
      }

      try {
        // Use WebFilePicker to pick multiple images
        final imageDataList = await WebFilePicker.pickMultipleImages();
        if (imageDataList != null && imageDataList.isNotEmpty) {
          // Check if adding these images would exceed the limit
          if (totalImages + imageDataList.length > imageLimit) {
            // Calculate how many images we can add
            int availableSlots = imageLimit - totalImages;
            if (availableSlots > 0) {
              // Add only the number of images that fit within the limit
              webImages.addAll(imageDataList.take(availableSlots));
              webImagesCtrl.sink.add(webImages);
            }

            // Store context in a local variable to avoid BuildContext across async gaps warning
            final currentContext = context;
            if (currentContext.mounted) {
              return CommonMethods.toastMessage(
                  "${AppStrings.youCantAddMoreThen} $imageLimit images",
                  currentContext);
            }
            return;
          }

          // Add all selected images to the list
          webImages.addAll(imageDataList);

          // Stream the updated image list
          webImagesCtrl.sink.add(webImages);
        }
      } catch (e) {
        debugPrint("Error picking web images: $e");
      }
      return;
    }

    // For mobile platform
    //Check if image count has reached the limit
    if (selectedImage.length >= imageLimit) {
      return CommonMethods.toastMessage(
          "${AppStrings.youCantAddMoreThen} $imageLimit images", context);
    }

    //Open the AddImageOptionScreen
    Widget screen = const AddImageOptionScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //If value is null, no images were selected
      if (value == null) {
        return;
      }

      // Check how many images are being added
      List<File> newImages = (value as List).cast<File>();
      int totalImagesAfterAddition = selectedImage.length + newImages.length;

      // If adding the new images exceeds the limit, show a warning and add only up to the limit
      if (totalImagesAfterAddition > imageLimit) {
        int imagesToAdd = imageLimit -
            selectedImage.length; // Add only the number of images that fit
        selectedImage.insertAll(
            selectedImage.length, newImages.take(imagesToAdd).toList());

        //Stream the updated image list
        selectedImageCtrl.sink.add(selectedImage);
        return CommonMethods.toastMessage(
            "${AppStrings.youCantAddMoreThen} $imageLimit images", context);
      } else {
        // Add all selected images if within the limit
        selectedImage.addAll(newImages);
      }

      //Stream the updated image list
      selectedImageCtrl.sink.add(selectedImage);
    });
  }
//endregion

  //region On tap remove image
  void onTapRemoveImage({required File filePath}) {
    //Remove image from the list
    selectedImage.removeWhere((element) => element == filePath);
    //Stream selected image
    selectedImageCtrl.sink.add(selectedImage);
  }
  //endregion

  //region On tap remove web image
  void onTapRemoveWebImage({required int index}) {
    if (index >= 0 && index < webImages.length) {
      //Remove image from the list
      webImages.removeAt(index);
      //Stream selected image
      webImagesCtrl.sink.add(webImages);
    }
  }
  //endregion

  //region Create post
  void addPost() {
    // Check if both text and images are empty
    bool hasNoContent = addPostTextCtrl.text.trim().isEmpty &&
        selectedImage.isEmpty &&
        webImages.isEmpty;

    if (hasNoContent) {
      return CommonMethods.toastMessage(
          AppStrings.emptyPostCanNotBeAdded, context);
    }

    // Prepare tagged references
    List<Map<String, dynamic>> taggedReferences = _prepareTaggedReferences();

    // For web platform
    if (kIsWeb) {
      // Add post api call for web
      PostAndCommentUploadFiles().createWebPostApiCall(
        postText: cleanText(addPostTextCtrl.text),
        webImages: webImages,
        taggedReferences: taggedReferences,
      );
    } else {
      // Add post api call for mobile
      PostAndCommentUploadFiles().createPostApiCall(
        postText: cleanText(addPostTextCtrl.text),
        selectedImage: selectedImage,
        taggedReferences: taggedReferences,
      );
    }

    // Close screen
    Navigator.pop(context);
  }

  List<Map<String, dynamic>> _prepareTaggedReferences() {
    List<Map<String, dynamic>> taggedReferences = [];
    int order = 1;

    // Add mentions from text field
    for (MentionData mention in mentions) {
      String? reference;
      String? type;

      switch (mention.type) {
        case 'USER':
          reference = mention.userReference;
          type = 'USER';
          break;
        case 'STORE':
          reference = mention.storeReference;
          type = 'STORE';
          break;
        case 'PRODUCT':
          reference = mention.productReference;
          type = 'PRODUCT';
          break;
      }

      if (reference != null && type != null) {
        taggedReferences.add({
          'reference': reference,
          'type': type,
          'order': order++,
        });
      }
    }

    // Add tagged items from access settings
    for (SuggestionItem item in taggedItemsFromAccess) {
      if (item.reference != null && item.type != null) {
        taggedReferences.add({
          'reference': item.reference!,
          'type': item.type!,
          'order': order++,
        });
      }
    }

    return taggedReferences;
  }
  //endregion

  //region Get single post
  Future<void> getSinglePost({required String postReference}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      PostDetail postDetail =
          await PostService().getSinglePost(postReference: postReference);

      //Add updated data into data model
      postDataModel.addPostIntoList(postList: [postDetail]);
      //Clear text field and images
      selectedImage.clear();
      addPostTextCtrl.clear();
      //Add the flag that post is adding
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? addUpdatePostScreenStateCtrl.sink.add(AddPostScreenState.Failed)
          : null;
      return;
    }
  }
//endregion

  //region Clean text
  String cleanText(String input) {
    // Trim spaces and newlines from start and end
    String sanitized = input.trim();

    // Check if the resulting text starts and ends with non-space characters
    if (sanitized.isNotEmpty &&
        sanitized[0] != ' ' &&
        sanitized[sanitized.length - 1] != ' ') {
      return sanitized;
    }

    // Return an empty string if conditions are not met
    return '';
  }

  //endregion

  //region Typing Suggestions
  void _onTextChanged() {
    String text = addPostTextCtrl.text;
    int cursorPosition = addPostTextCtrl.selection.baseOffset;

    // Find the current word being typed
    String currentWord = _getCurrentWord(text, cursorPosition);

    if (currentWord.startsWith('@') && currentWord.length > 1) {
      String query = currentWord;
      if (query != currentQuery) {
        currentQuery = query;
        suggestionsOffset = 0;
        _fetchSuggestions(query);
      }
    } else {
      _hideSuggestions();
    }
  }

  String _getCurrentWord(String text, int cursorPosition) {
    if (cursorPosition <= 0 || cursorPosition > text.length) return '';

    // Find the start of the current word (look for @)
    int start = cursorPosition - 1;
    while (start > 0 && text[start - 1] != ' ' && text[start - 1] != '\n') {
      start--;
    }

    // If we found an @ symbol, this might be a mention
    if (start < text.length && text[start] == '@') {
      // For mentions, we need to handle product mentions which can contain spaces
      // Look for the complete mention pattern
      int end = cursorPosition;
      bool foundApostrophe = false;

      // First, find the end of the basic mention (until space or apostrophe)
      while (end < text.length && text[end] != ' ' && text[end] != '\n') {
        if (text[end] == "'") {
          foundApostrophe = true;
        }
        end++;
      }

      // If we found an apostrophe and there's more text, this might be a product mention
      if (foundApostrophe && end < text.length - 1 && text.substring(start, end).contains("'s")) {
        // Continue until we find a space or newline that's not part of the product name
        while (end < text.length && text[end] != '\n') {
          if (text[end] == ' ') {
            // Check if this space is followed by another mention or end of text
            if (end + 1 >= text.length || text[end + 1] == '@' || text[end + 1] == '\n') {
              break;
            }
          }
          end++;
        }
      }

      return text.substring(start, end);
    } else {
      // For non-mentions, use the original logic
      int end = cursorPosition;
      while (end < text.length && text[end] != ' ' && text[end] != '\n') {
        end++;
      }
      return text.substring(start, end);
    }
  }

  Future<void> _fetchSuggestions(String query, {bool loadMore = false}) async {
    if (isLoadingSuggestions) return;

    isLoadingSuggestions = true;
    suggestionsLoadingCtrl.sink.add(true);

    if (!loadMore) {
      suggestions.clear();
      suggestionsOffset = 0;
    }

    try {
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      TypingSuggestionsResponse response =
          await _typingSuggestionsService.getTypingSuggestions(
        query: query,
        limit: suggestionsLimit,
        offset: suggestionsOffset,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      if (loadMore) {
        suggestions.addAll(response.results ?? []);
      } else {
        suggestions = response.results ?? [];
      }

      hasMoreSuggestions = (response.results?.length ?? 0) == suggestionsLimit;
      suggestionsOffset += suggestionsLimit;

      showSuggestions = suggestions.isNotEmpty;
      suggestionsCtrl.sink.add(suggestions);
      showSuggestionsCtrl.sink.add(showSuggestions);
    } catch (e) {
      // Handle error silently for typing suggestions
      showSuggestions = false;
      showSuggestionsCtrl.sink.add(false);
    } finally {
      isLoadingSuggestions = false;
      suggestionsLoadingCtrl.sink.add(false);
    }
  }

  void _hideSuggestions() {
    showSuggestions = false;
    suggestions.clear();
    currentQuery = '';
    showSuggestionsCtrl.sink.add(false);
    suggestionsCtrl.sink.add([]);
  }

  void loadMoreSuggestions() {
    if (hasMoreSuggestions &&
        !isLoadingSuggestions &&
        currentQuery.isNotEmpty) {
      _fetchSuggestions(currentQuery, loadMore: true);
    }
  }

  void onSuggestionTap(SuggestionItem suggestion) {
    if (suggestion.type == 'STORE') {
      _showStoreOptionsDialog(suggestion);
    } else {
      _insertMention(suggestion);
    }
  }

  void _showStoreOptionsDialog(SuggestionItem storeItem) {
    showDialog(
      context: context,
      builder: (context) => StoreMentionOptionsDialog(
        storeItem: storeItem,
        onOptionSelected: (store, showProducts) {
          if (showProducts) {
            _showStoreProductsDialog(store);
          } else {
            _insertMention(store);
          }
        },
      ),
    );
  }

  void _showStoreProductsDialog(SuggestionItem storeItem) {
    showDialog(
      context: context,
      builder: (context) => StoreProductsSelectionDialog(
        storeItem: storeItem,
        onProductSelected: (product) {
          _insertMention(product);
        },
      ),
    );
  }

  void _insertMention(SuggestionItem suggestion) {
    String text = addPostTextCtrl.text;
    int cursorPosition = addPostTextCtrl.selection.baseOffset;

    // Use the same logic as _getCurrentWord to find the mention boundaries
    String currentMention = _getCurrentWord(text, cursorPosition);

    // Find the actual start position of the mention
    int start = text.lastIndexOf(currentMention, cursorPosition - 1);
    if (start == -1) {
      // Fallback to original logic
      start = cursorPosition - 1;
      while (start > 0 && text[start - 1] != ' ' && text[start - 1] != '\n') {
        start--;
      }
    }

    int end = start + currentMention.length;

    // Create mention text based on type
    String mentionText = _createMentionText(suggestion);

    // Replace the current @mention with the selected one
    String newText = text.replaceRange(start, end, mentionText);

    // Store mention data
    MentionData mentionData = MentionData(
      type: suggestion.type,
      displayText: mentionText,
    );

    switch (suggestion.type) {
      case 'USER':
        mentionData.userReference = suggestion.reference;
        break;
      case 'STORE':
        mentionData.storeReference = suggestion.reference;
        break;
      case 'PRODUCT':
        mentionData.productReference = suggestion.reference;
        mentionData.storeReference =
            suggestion.storeHandle; // Store handle for products
        break;
    }

    mentions.add(mentionData);

    // Update text and cursor position
    addPostTextCtrl.text = newText;
    int newCursorPosition = start + mentionText.length;
    addPostTextCtrl.selection =
        TextSelection.collapsed(offset: newCursorPosition);

    // Hide suggestions
    _hideSuggestions();
  }

  String _createMentionText(SuggestionItem suggestion) {
    switch (suggestion.type) {
      case 'USER':
        return '@${suggestion.primaryText}';
      case 'STORE':
        return '@${suggestion.primaryText}';
      case 'PRODUCT':
        return "@${suggestion.storeName}'s ${suggestion.primaryText}";
      default:
        return '@${suggestion.primaryText}';
    }
  }
  //endregion

  //region Tag Store Product Member
  void onTapTagStoreProductMember() {
    _showTaggingBottomSheet();
  }

  void _showTaggingBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'Tag Store, Product or Member',
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Suggestions list
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                child: StreamBuilder<List<SuggestionItem>>(
                  stream: suggestionsCtrl.stream,
                  initialData: const [],
                  builder: (context, snapshot) {
                    return StreamBuilder<bool>(
                      stream: suggestionsLoadingCtrl.stream,
                      initialData: false,
                      builder: (context, loadingSnapshot) {
                        return EnhancedTypingSuggestionsOverlay(
                          suggestions: snapshot.data ?? [],
                          onSuggestionTap: (suggestion) {
                            _addTaggedItemFromAccess(suggestion);
                            Navigator.pop(context);
                          },
                          isLoading: loadingSnapshot.data ?? false,
                          onLoadMore: loadMoreSuggestions,
                          hasMore: hasMoreSuggestions,
                          showSearchBar: true,
                          onSearchChanged: (value) {
                            if (value.isNotEmpty) {
                              _fetchTaggingSuggestions('@$value');
                            } else {
                              _hideTaggingSuggestions();
                            }
                          },
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _fetchTaggingSuggestions(String query) async {
    if (isLoadingSuggestions) return;

    isLoadingSuggestions = true;
    suggestionsLoadingCtrl.sink.add(true);

    suggestions.clear();
    suggestionsOffset = 0;

    try {
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      TypingSuggestionsResponse response =
          await _typingSuggestionsService.getTypingSuggestions(
        query: query,
        limit: suggestionsLimit,
        offset: suggestionsOffset,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      suggestions = response.results ?? [];
      hasMoreSuggestions = (response.results?.length ?? 0) == suggestionsLimit;
      suggestionsOffset += suggestionsLimit;

      suggestionsCtrl.sink.add(suggestions);
    } catch (e) {
      // Handle error silently
      suggestionsCtrl.sink.add([]);
    } finally {
      isLoadingSuggestions = false;
      suggestionsLoadingCtrl.sink.add(false);
    }
  }

  void _hideTaggingSuggestions() {
    suggestions.clear();
    suggestionsCtrl.sink.add([]);
  }

  void _addTaggedItemFromAccess(SuggestionItem suggestion) {
    // Check if item is already tagged
    bool alreadyTagged = taggedItemsFromAccess.any((item) =>
        item.reference == suggestion.reference && item.type == suggestion.type);

    if (!alreadyTagged) {
      taggedItemsFromAccess.add(suggestion);
      taggedItemsFromAccessCtrl.sink.add(taggedItemsFromAccess);

      // Switch to the tab of the newly added item
      if (suggestion.type != null) {
        selectTagTab(suggestion.type!);
      }
    }
  }

  void removeTaggedItem(int index) {
    // Get filtered items for current tab
    List<SuggestionItem> filteredItems = getFilteredTaggedItems();

    if (index >= 0 && index < filteredItems.length) {
      // Find the item to remove in the original list
      SuggestionItem itemToRemove = filteredItems[index];
      taggedItemsFromAccess.removeWhere((item) =>
          item.reference == itemToRemove.reference &&
          item.type == itemToRemove.type);
      taggedItemsFromAccessCtrl.sink.add(taggedItemsFromAccess);
    }
  }

  void selectTagTab(String tabType) {
    selectedTagTab = tabType;
    selectedTagTabCtrl.sink.add(selectedTagTab);
  }

  List<SuggestionItem> getFilteredTaggedItems() {
    return taggedItemsFromAccess.where((item) => item.type == selectedTagTab).toList();
  }
  //endregion

//region Dispose
  void dispose() {
    addPostTextCtrl.removeListener(_onTextChanged);
    addUpdatePostScreenStateCtrl.close();
    selectedImageCtrl.close();
    webImagesCtrl.close();
    suggestionsCtrl.close();
    showSuggestionsCtrl.close();
    suggestionsLoadingCtrl.close();
    taggedItemsFromAccessCtrl.close();
    selectedTagTabCtrl.close();
  }
//endregion
}
