import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class RoleSelectionBloc {
  // region Common Methods
  BuildContext context;
  final String userReference;
  final ImagePicker picker = ImagePicker();
  final ImageCropper imageCrop = ImageCropper();
  String? userProfilePicturePath;
  List<String> selectedRoles = [];

  // endregion

  //region Controller
  final userProfilePictureCtrl = StreamController<String>.broadcast();
  final TextEditingController inviteCodeTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  RoleSelectionBloc(this.context, this.userReference);

  // endregion

  //region Open Gallery
  void openGallery() async {
    try {
      XFile? galleryImage = await picker.pickImage(source: ImageSource.gallery);
      if (galleryImage == null) return;
      
      // Crop image
      crop(file: File(galleryImage.path));
    } catch (e) {
      debugPrint("Error selecting image: $e");
    }
  }
  //endregion

  //region Image crop
  crop({required File file}) async {
    File? croppedFile = await CommonMethods.imageCrop(file: File(file.path));
    
    if (croppedFile != null) {
      userProfilePicturePath = croppedFile.path;
      userProfilePictureCtrl.sink.add(croppedFile.path);
    }
  }
  //endregion

  //region Validate Roles
  bool validateRoles() {
    if (selectedRoles.isEmpty) {
      CommonMethods.toastMessage(AppStrings.pleaseSelectAtLeaseOneRole, context);
      return false;
    }
    return true;
  }
  //endregion

  //region Dispose
  void dispose() {
    userProfilePictureCtrl.close();
  }
  //endregion
}
