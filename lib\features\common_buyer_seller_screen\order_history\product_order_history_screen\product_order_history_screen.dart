import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_history/product_order_history_screen/product_order_history_bloc.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/sub_order_history_response/sub_order_history_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
//region ProductOrderHistoryScreen
class ProductOrderHistoryScreen extends StatefulWidget {
  final SubOrder subOrder;
  const ProductOrderHistoryScreen({Key? key, required this.subOrder}) : super(key: key);

  @override
  State<ProductOrderHistoryScreen> createState() => _ProductOrderHistoryScreenState();
}
//endregion


class _ProductOrderHistoryScreenState extends State<ProductOrderHistoryScreen> {
  //region Bloc
  late ProductOrderHistoryBloc productOrderHistoryBloc;
  //endregion
  //region Init
  @override
  void initState() {
    productOrderHistoryBloc = ProductOrderHistoryBloc(context,widget.subOrder);
    productOrderHistoryBloc.init();
    super.initState();
  }
  //endregion
  //region Dispose
  @override
  void dispose() {
    productOrderHistoryBloc.dispose();
    super.dispose();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body:body() ,

    );
  }


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.productOrderHistory,
      isCartVisible:AppConstants.appData.isStoreView!?false:true,
      isMembershipVisible:true,

    );
  }

  //endregion

//region Body
  Widget body(){
    return ListView(
      children: [
        product(),
        historyList(),
      ],
    );
  }
//endregion

//region Product
Widget product(){
    return Container(
      child:Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        child: AppCommonWidgets.subOrderInfo(subOrder:widget.subOrder,

            isCheckBoxVisible: false,
            isPriceDetailVisible: true,
            isStatusVisible: false,
            isArrowVisible: false, context: context

        ),
      )
    );
}
//endregion

  //region Price, delivery fee and return fee
  Widget priceDeliveryReturn(
      {required int productQuantity, required int productPrice, required int totalProductPrice, required String productDeliveryFee}) {
    return SizedBox(
      height: 20,

      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          appText("$productQuantity x ₹ $productPrice = ₹ $totalProductPrice",
              color: AppColors.writingColor2, fontWeight: FontWeight.w400, fontFamily: AppConstants.rRegular, fontSize: 14, maxLine: 1),
          appText("DF: ₹ $productDeliveryFee",
              color: AppColors.writingColor2, fontWeight: FontWeight.w400, fontFamily: AppConstants.rRegular, fontSize: 14, maxLine: 1),
          appText("Return: 10days",
              color: AppColors.writingColor2, fontWeight: FontWeight.w400, fontFamily: AppConstants.rRegular, fontSize: 14, maxLine: 1),
        ],
      ),
    );
  }
//endregion





  //region History
  Widget historyList(){
    return StreamBuilder<ProductOrderHistoryState>(
      stream: productOrderHistoryBloc.productOrderHistoryCtrl.stream,
      initialData:ProductOrderHistoryState.Loading ,
      builder: (context, snapshot) {
        if(snapshot.data == ProductOrderHistoryState.Success){
          return AppDropDown(
            dropDownName: AppStrings.history,
            dropdownArrowVisible: false,
            dropDownWidget: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: Column(
                children: [
                  ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: productOrderHistoryBloc.subOrderHistoryResponse.subOrderHistoryList!.length,
                      itemBuilder: (buildContext, index) {
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            historyBox(subOrderHistory: productOrderHistoryBloc.subOrderHistoryResponse.subOrderHistoryList![index]),
                            productOrderHistoryBloc.subOrderHistoryResponse.subOrderHistoryList!.length-1 == index?const SizedBox():  Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                                  child: RotatedBox(
                                    quarterTurns: 1,
                                    child: Container(
                                      height: 2,
                                      width: 10,
                                      color: AppColors.appBlack,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                                  child: RotatedBox(
                                    quarterTurns: 1,
                                    child: Container(
                                      height: 2,
                                      width: 10,
                                      color: AppColors.appBlack,
                                    ),
                                  ),
                                )
                              ],
                            )
                          ],
                        );
                      }),
                ],
              ),
            ),
            initialExpand: true,
            collapsedWidget: const SizedBox(),
          );

        }
        return  Center(child: AppCommonWidgets.appCircularProgress());
      }
    );
  }
  //endregion


//region history box
  Widget historyBox({required SubOrderHistory subOrderHistory}) {
    return Stack(
      children: [
        Container(
          //margin: const EdgeInsets.only(bottom: 10),
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(color: AppColors.textFieldFill1, borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: SvgPicture.asset(
                  AppImages.dot,
                  width: 10,
                  height: 10,
                  color: AppColors.appBlack10,
                ),
              ),
              horizontalSizedBox(5),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(CommonMethods.subOrderStatusToString(subOrderStatus: subOrderHistory.title!) ,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                    Visibility(
                      visible: subOrderHistory.description!.isNotEmpty,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child:
                        Text(subOrderHistory.description!,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
                      ),
                    ),
                    Text(subOrderHistory.date!,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),

                  ],
                ),
              ),
              //Expanded(child: horizontalSizedBox(10)),
            ],
          ),
        ),
      ],
    );
  }
//endregion



}
