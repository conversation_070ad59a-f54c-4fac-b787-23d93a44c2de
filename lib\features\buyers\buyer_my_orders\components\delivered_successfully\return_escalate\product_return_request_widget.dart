import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/return_escalate/product_return_reason_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/return_escalate/return_condition_checklist_widget.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductReturnRequestWidget extends StatelessWidget {
  final SubOrder subOrder;
  final TextEditingController textController;
  final List<String> returnConditions;
  final List<bool> conditionsCheckedState;
  final Function(List<bool>) onConditionsChanged;
  final bool isLastItem;

  const ProductReturnRequestWidget({
    Key? key,
    required this.subOrder,
    required this.textController,
    required this.returnConditions,
    required this.conditionsCheckedState,
    required this.onConditionsChanged,
    this.isLastItem = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: AppColors.appBlack.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 1. Product Header
          _buildProductHeader(context),

          verticalSizedBox(20),

          // 2. Return Conditions Checklist
          if (returnConditions.isNotEmpty)
            ReturnConditionChecklistWidget(
              conditions: returnConditions,
              initialCheckedState: conditionsCheckedState,
              onConditionsChanged: onConditionsChanged,
            ),

          // 3. Return Reason Text Field
          _buildReturnReasonField(context),

          // Divider if not the last item
          if (!isLastItem)
            const Padding(
              padding: EdgeInsets.only(top: 24),
              child: Divider(color: AppColors.lightGray, height: 1, thickness: 1),
            ),
        ],
      ),
    );
  }

  Widget _buildProductHeader(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Product image
        ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          child: Container(
            color: AppColors.textFieldFill1,
            height: 60,
            width: 60,
            child: extendedImage(
              subOrder.productImage ?? "",
              context,
              75,
              75,
              customPlaceHolder: AppImages.productPlaceHolder,
              cache: true,
            ),
          ),
        ),

        // Spacing
        horizontalSizedBox(12),

        // Product name and details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product brand and name
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: subOrder.productBrand ?? "",
                      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                    TextSpan(
                      text: " ${subOrder.productName ?? ""}",
                      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                    ),
                  ],
                ),
              ),

              // Price and quantity info
              Text(
                "₹${subOrder.sellingPrice} × ${subOrder.productQuantity} = ₹${(subOrder.sellingPrice ?? 0) * (subOrder.productQuantity ?? 1)}",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReturnReasonField(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0, top: 16.0),
          child: Text(
            "Return Reason",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
        ),

        // Return reason text field
        AppTextFields.allTextField(
          context: context,
          maxEntry: 200,
          maxLines: 3,
          minLines: 3,
          textEditingController: textController,
          hintText: "${AppStrings.returnReason} for this product",
        ),
      ],
    );
  }
}
