import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class DisclaimerBottomSheet extends StatelessWidget {
  final Product product;
  final Function onTapShowAnyways;

  const DisclaimerBottomSheet({
    Key? key,
    required this.product,
    required this.onTapShowAnyways,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Parse the disclaimer message which is in format "title|message"
    String title = AppStrings.shopWithCare;
    String message = AppStrings.thisStoreIsNotYetPublic;

    if (product.disclaimerMessage != null &&
        product.disclaimerMessage!.isNotEmpty) {
      List<String> parts = product.disclaimerMessage!.split('|');
      if (parts.isNotEmpty) {
        title = parts[0].trim();
      }
      if (parts.length >= 2) {
        message = parts[1].trim();
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style:
                AppTextStyle.sectionHeading(textColor: AppColors.writingBlack0),
          ),
          verticalSizedBox(5),
          Text(
            message,
            textAlign: TextAlign.center,
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack2),
          ),
          verticalSizedBox(20),
          SizedBox(
            width: double.infinity, // Make the button take full width
            child: CupertinoButton(
              padding: const EdgeInsets.symmetric(
                  vertical: 16), // Add more vertical padding
              color: AppColors.textFieldFill0,
              borderRadius: BorderRadius.circular(100),
              child: Text(
                AppStrings.continueAnyway,
                style: AppTextStyle.contentText0(textColor: AppColors.appWhite),
              ),
              onPressed: () {
                // Close the bottom sheet
                Navigator.pop(context);
                // Call the callback function
                onTapShowAnyways();
              },
            ),
          )
        ],
      ),
    );
  }

  Widget verticalSizedBox(double height) {
    return SizedBox(height: height);
  }
}
