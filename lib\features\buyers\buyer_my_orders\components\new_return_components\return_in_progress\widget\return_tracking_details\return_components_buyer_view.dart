import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_tracking_details/return_tracking_details_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnSellerBuyerView extends StatelessWidget {
  final ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  const ReturnSellerBuyerView(
      {Key? key, required this.returnTrackingDetailsBloc})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Self Return",
          style: TextStyle(
            color: AppColors.appBlack,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.name,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: Text(
            returnTrackingDetailsBloc.nameTextCtrl.text.isNotEmpty
                ? returnTrackingDetailsBloc.nameTextCtrl.text
                : "Not provided",
            style: const TextStyle(color: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.phoneNumber,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: Row(
            children: [
              Expanded(
                child: Text(
                  returnTrackingDetailsBloc.phoneNumberTextCtrl.text.isNotEmpty
                      ? "+91 ${returnTrackingDetailsBloc.phoneNumberTextCtrl.text}"
                      : "Not provided",
                  style: const TextStyle(color: AppColors.appBlack),
                ),
              ),
              if (returnTrackingDetailsBloc.phoneNumberTextCtrl.text.isNotEmpty)
                TextButton(
                  onPressed: () {
                    // Copy phone number to clipboard
                    Clipboard.setData(ClipboardData(
                        text:
                            "+91${returnTrackingDetailsBloc.phoneNumberTextCtrl.text}"));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Phone number copied to clipboard')),
                    );
                  },
                  child: Text(
                    'Copy',
                    style: TextStyle(color: AppColors.brandBlack),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class ReturnLogisticBuyerView extends StatelessWidget {
  final ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  const ReturnLogisticBuyerView(
      {Key? key, required this.returnTrackingDetailsBloc})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Return by Logistics Partner",
          style: TextStyle(
            color: AppColors.appBlack,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.logisticsPartner,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: Text(
            returnTrackingDetailsBloc.logisticPartnerTextCtrl.text.isNotEmpty
                ? returnTrackingDetailsBloc.logisticPartnerTextCtrl.text
                : "Not assigned",
            style: const TextStyle(color: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.trackingNumber,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: Row(
            children: [
              Expanded(
                child: Text(
                  returnTrackingDetailsBloc
                          .trackingNumberTextCtrl.text.isNotEmpty
                      ? returnTrackingDetailsBloc.trackingNumberTextCtrl.text
                      : "Not assigned",
                  style: const TextStyle(color: AppColors.appBlack),
                ),
              ),
              if (returnTrackingDetailsBloc
                  .trackingNumberTextCtrl.text.isNotEmpty)
                TextButton(
                  onPressed: () {
                    // Copy tracking number to clipboard
                    Clipboard.setData(ClipboardData(
                        text: returnTrackingDetailsBloc
                            .trackingNumberTextCtrl.text));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Tracking number copied to clipboard')),
                    );
                  },
                  child: Text(
                    'Copy',
                    style: TextStyle(color: AppColors.brandBlack),
                  ),
                ),
            ],
          ),
        ),
        verticalSizedBox(10),
        AppTitleAndOptions(
          title: AppStrings.trackingLink,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: InkWell(
            onTap: () async {
              if (returnTrackingDetailsBloc
                  .trackingLinkTextCtrl.text.isNotEmpty) {
                CommonMethods.opeAppWebView(
                    context: context,
                    webUrl:
                        returnTrackingDetailsBloc.trackingLinkTextCtrl.text);
              }
            },
            child: Text(
              returnTrackingDetailsBloc.trackingLinkTextCtrl.text.isNotEmpty
                  ? returnTrackingDetailsBloc.trackingLinkTextCtrl.text
                  : "Not available",
              style: TextStyle(
                color: returnTrackingDetailsBloc
                        .trackingLinkTextCtrl.text.isNotEmpty
                    ? AppColors.brandBlack
                    : AppColors.appBlack,
                decoration: returnTrackingDetailsBloc
                        .trackingLinkTextCtrl.text.isNotEmpty
                    ? TextDecoration.underline
                    : null,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
