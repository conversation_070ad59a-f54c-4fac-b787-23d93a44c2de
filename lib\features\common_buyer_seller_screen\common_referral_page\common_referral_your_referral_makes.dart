import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page_bloc.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class CommonReferralYourReferralMakes extends StatefulWidget {
  final CommonReferralPageBloc commonReferralPageBloc;
  const CommonReferralYourReferralMakes(
      {super.key, required this.commonReferralPageBloc});

  @override
  State<CommonReferralYourReferralMakes> createState() =>
      _CommonReferralYourReferralMakesState();
}

class _CommonReferralYourReferralMakesState
    extends State<CommonReferralYourReferralMakes> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(
        horizontal: 15,
      ),
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
        color: Colors.transparent, // Background color
        border: Border.all(
          color: AppColors.brandBlack,
          width: 1, // Border width
        ),
        borderRadius: BorderRadius.circular(15), // Rounded corners
      ),
      child: detail(),
    );
  }
//endregion

//region Detail
  Widget detail() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text("Your Referral Makes a Difference",
            style: AppTextStyle.introSlideTitle(textColor: AppColors.appBlack)
                .copyWith(
                    fontSize: 20, fontFamily: AppConstants.leagueSemiBold)),
        const SizedBox(height: 30),
        imageWithText()
      ],
    );
  }
//endregion

//region Image with text
  Widget pointWithEmoji(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15, right: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 5, right: 10, left: 10),
            child: Text(
              "👉",
              style: TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  Widget imageWithText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        pointWithEmoji(
            "Invite your friends. Family. Neighbors. Colleagues. Show them how easy it is to buy Swadeshi."),
        pointWithEmoji(
            "For every rupee spent here, another rupee stays in India."),
        pointWithEmoji(
            "Know a small business, a local seller? Bring them in. Let them sell commission-free."),
        pointWithEmoji(
            "For every person you bring, the more people we bring in, the faster we flip the system in our favor."),
        SizedBox(height: 20),
        inviteFriendsAndStore(),
      ],
    );
  }
//endregion

//region Invite friends and and store
  Widget inviteFriendsAndStore() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        CommonMethods.share(
            "${AppConstants.appData.isUserView! ? AppStrings.userInviteeMessage : AppStrings.storeInviteeMessage}\n${AppConstants.domainName}?ref=${widget.commonReferralPageBloc.inviteCode}");
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        margin: const EdgeInsets.only(left: 10, right: 10, top: 13),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius:
                BorderRadius.circular(MediaQuery.of(context).size.width * 0.5)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              AppImages.basketWithStar,
              height: 40,
              width: 40,
            ),
            const SizedBox(width: 10),
            Text(
              "Invite with your code",
              style: AppTextStyle.access1(textColor: AppColors.appWhite),
            ),
          ],
        ),
      ),
    );
  }
//endregion

  //region Image and detail
  Widget imageAndDetail(
      {required AllAboutInfinityDetail allAboutInfinityDetail}) {
    return Container(
      // margin: const EdgeInsets.symmetric(vertical: 10),
      margin: const EdgeInsets.only(top: 13),
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            allAboutInfinityDetail.image,
            height: 40,
            width: 40,
          ),
          const SizedBox(width: 5),
          //Get up to
          Expanded(
            child: Column(
              children: [
                //Title
                Text(
                  allAboutInfinityDetail.title!,
                  style: AppTextStyle.access0(textColor: AppColors.appBlack),
                ),
                //If sub title is null then reurn container
                allAboutInfinityDetail.subTitle != null
                    ? Text(
                        allAboutInfinityDetail.subTitle!,
                        style: AppTextStyle.smallText(
                            textColor: AppColors.writingBlack1),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ],
      ),
    );
  }
//endregion
}
