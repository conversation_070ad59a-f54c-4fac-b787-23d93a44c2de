import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/cart_delivery_address_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class CartDeliveryAddressScreen extends StatefulWidget {
  final SecureCheckoutBloc secureCheckoutBloc;

  const CartDeliveryAddressScreen(
      {super.key, required this.secureCheckoutBloc});

  @override
  State<CartDeliveryAddressScreen> createState() =>
      _CartDeliveryAddressScreenState();
}

class _CartDeliveryAddressScreenState extends State<CartDeliveryAddressScreen> {
  //region Bloc
  late CartDeliveryAddressBloc cartDeliveryAddressBloc;

  //endregion

  //region Init
  @override
  void initState() {
    cartDeliveryAddressBloc =
        CartDeliveryAddressBloc(context, widget.secureCheckoutBloc);
    cartDeliveryAddressBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    cartDeliveryAddressBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: body(),
    );
  }

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.savedAddresses,
        isCartVisible: false,
        isMembershipVisible: false);
  }

// endregion

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          addAddress(),
          //Add address
          addressList(),
        ],
      ),
    );
  }

//endregion

  //region Add new address
  Widget addAddress() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        cartDeliveryAddressBloc.onTapAddAddress();
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 15),
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
            color: AppColors.appWhite,
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            border: Border.all(color: AppColors.lightStroke)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            SvgPicture.asset(
              AppImages.plus,
              color: AppColors.appBlack,
              fit: BoxFit.fill,
            ),
            horizontalSizedBox(15),
            Text(
              AppStrings.addNewAddress,
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

//region Address list
  Widget addressList() {
    return StreamBuilder<CartDeliveryAddressState>(
      stream: cartDeliveryAddressBloc.cartAddressCtrl.stream,
      initialData: CartDeliveryAddressState.Success,
      builder: (context, snapshot) {
        //Success
        if (snapshot.data == CartDeliveryAddressState.Success) {
          return ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 15),
            shrinkWrap: true,
            itemCount: cartDeliveryAddressBloc.secureCheckoutBloc
                .shoppingCartAddressResponse.addressList!.length,
            itemBuilder: (context, index) {
              return InkWell(
                // onLongPress: () {
                //   editDelete(index);
                //   addressId = userAddressResponse.addressList![index].useraddressid.toString();
                // },
                onTap: () {
                  cartDeliveryAddressBloc.onSelectAddress(
                      shoppingCartAddress: cartDeliveryAddressBloc
                          .secureCheckoutBloc
                          .shoppingCartAddressResponse
                          .addressList![index]);
                  // onSelectAddress(userAddressResponse.addressList![index].useraddressid!, userAddressResponse.addressList![index]);
                  // sellerReturnWarrantyBloc.onSelectAddress(index);
                  // sellerReturnWarrantyBloc.editDelete();
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  // padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  decoration: BoxDecoration(
                      color: AppColors.appWhite,
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                      border: Border.all(color: AppColors.lightStroke)),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                    child: Slidable(
                      direction: Axis.horizontal,
                      endActionPane: ActionPane(
                        motion: const ScrollMotion(),
                        children: [
                          SlidableAction(
                            onPressed: (context) {
                              cartDeliveryAddressBloc.onTapEditAddress(
                                  shoppingCartAddress: cartDeliveryAddressBloc
                                      .secureCheckoutBloc
                                      .shoppingCartAddressResponse
                                      .addressList![index]);
                            },
                            backgroundColor: AppColors.brandBlack,
                            foregroundColor: Colors.white,
                            icon: Icons.edit,
                            label: 'Edit',
                          ),
                          SlidableAction(
                            onPressed: (context) {
                              cartDeliveryAddressBloc.deleteAddress(
                                  shoppingCartAddress: cartDeliveryAddressBloc
                                      .secureCheckoutBloc
                                      .shoppingCartAddressResponse
                                      .addressList![index]);
                            },
                            backgroundColor: AppColors.red,
                            foregroundColor: Colors.white,
                            icon: Icons.delete,
                            label: 'Delete',
                          ),
                        ],
                      ),
                      startActionPane: ActionPane(
                        motion: const ScrollMotion(),
                        children: [
                          SlidableAction(
                            onPressed: (context) {
                              cartDeliveryAddressBloc.onTapEditAddress(
                                  shoppingCartAddress: cartDeliveryAddressBloc
                                      .secureCheckoutBloc
                                      .shoppingCartAddressResponse
                                      .addressList![index]);
                            },
                            backgroundColor: AppColors.brandBlack,
                            foregroundColor: Colors.white,
                            icon: Icons.edit,
                            label: 'Edit',
                          ),
                          SlidableAction(
                            onPressed: (context) {
                              cartDeliveryAddressBloc.deleteAddress(
                                  shoppingCartAddress: cartDeliveryAddressBloc
                                      .secureCheckoutBloc
                                      .shoppingCartAddressResponse
                                      .addressList![index]);
                            },
                            backgroundColor: AppColors.red,
                            foregroundColor: Colors.white,
                            icon: Icons.delete,
                            label: 'Delete',
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Container(
                              height: 15,
                              width: 15,
                              decoration: BoxDecoration(
                                  color: (cartDeliveryAddressBloc
                                              .secureCheckoutBloc
                                              .selectedAddress
                                              .useraddressid ==
                                          0)
                                      ? AppColors.appWhite
                                      : (cartDeliveryAddressBloc
                                                  .secureCheckoutBloc
                                                  .selectedAddress
                                                  .useraddressid ==
                                              cartDeliveryAddressBloc
                                                  .secureCheckoutBloc
                                                  .shoppingCartAddressResponse
                                                  .addressList![index]
                                                  .useraddressid!)
                                          ? AppColors.brandBlack
                                          : AppColors.appWhite,
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10)),
                                  border:
                                      Border.all(color: AppColors.lightStroke)),
                            ),
                            horizontalSizedBox(15),
                            Expanded(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    cartDeliveryAddressBloc
                                        .secureCheckoutBloc
                                        .shoppingCartAddressResponse
                                        .addressList![index]
                                        .name!,
                                    maxLines: 1,
                                    style: AppTextStyle.contentHeading0(
                                        textColor: AppColors.appBlack),
                                  ),
                                  verticalSizedBox(10),
                                  Text(
                                    "${cartDeliveryAddressBloc.secureCheckoutBloc.shoppingCartAddressResponse.addressList![index].address},${cartDeliveryAddressBloc.secureCheckoutBloc.shoppingCartAddressResponse.addressList![index].city},${cartDeliveryAddressBloc.secureCheckoutBloc.shoppingCartAddressResponse.addressList![index].state},${cartDeliveryAddressBloc.secureCheckoutBloc.shoppingCartAddressResponse.addressList![index].pincode}",
                                    // overflow: TextOverflow.fade,
                                    // softWrap: false,
                                    style: AppTextStyle.contentText0(
                                        textColor: AppColors.appBlack),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }
        //Failed
        if (snapshot.data == CartDeliveryAddressState.Failed) {
          return AppCommonWidgets.errorWidget(onTap: () {});
        }
        return const SizedBox();
      },
    );
  }
//endregion
}
