import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/buyer_product_details_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_products_pagination.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/common_buyer_seller_screen/update_stock/update_stock.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/liked_user_or_stores/liked_user_or_store_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_details/edit_product_details_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/model/shopping_cart_responses/add_to_cart_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart' as products;
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/product_comment_services/product_comment_services.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';

import '../../../../../model/shopping_cart_responses/cart_details_response.dart';
import '../../../../../model/store_product_response/store_product_response.dart';

enum BuyerViewProductState { Loading, Success, Failed }

class BuyerViewProductBloc {
  // region Common Variables
  BuildContext context;
  bool shareBottomSheetVisibility = false;
  bool commentBottomSheetVisibility = false;
  String storeHandle = "";
  bool saveStatus = false;
  final String storeReference;
  final String? searchedText;

  final isFromAddProduct ;
  //region Single product
  // final String productReference;
  //Is sub hading visible
  bool isSubHeadingVisible = false;
  final initialProductIndex;
  final SearchScreenEnum openingFrom;
  ///Buyer view product pagination
  late BuyerViewProductPagination buyerViewProductPagination;


  ///Get Product Image
  late ProductAndImageServices productAndImageServices;
  // late ProductImageResponse productImageResponse;

  //endregion
  ///Single store info
  // late SingleStoreInfoResponse singleStoreInfoResponse;
  // late SingleStoreInfoServices singleStoreInfoServices;
  ///DeepLink
  // late DeepLinkServices deepLinkServices;
  // late DeepLinkCreateResponse deepLinkCreateResponse;
  String productDeepLink = '';
  // final products.StoreProductResponse storeProductResponse;

  final List<Product> productList;

  ///Get Product All Comment
  late ProductCommentServices productCommentServices;
  late ProductAllCommentResponse productAllCommentResponse;

  ///Add to cart
  late ShoppingCartServices shoppingCartServices;
  ///Cart quantity data model
  late ShoppingCartQuantityDataModel shoppingCartQuantityDataModel;

  ///Cart Items Response
  // late GetCartItemResponses getCartItemResponses;
  late GetCartDetailsResponse cartDetailsResponse;

  ///Add to cart response
  late AddToCartResponse addToCartResponse;

  // endregion
  ///Store Product Services
  late StoreProductServices storeProductServices;



//region Page View Controller
  PageController pageController = PageController();
  final ScrollController scrollController = ScrollController();

  //endregion
  //region Scroll controller
  //endregion

  //region Controller
  final singleProductViewCtrl = StreamController<BuyerViewProductState>.broadcast();
  final saveCtrl = StreamController<BuyerViewProductState>.broadcast();
  final sliderCtrl = StreamController<int>.broadcast();
  final shareBottomSheetCtrl = StreamController<bool>.broadcast();
  final commentBottomSheetCtrl = StreamController<bool>.broadcast();
  final buyNowAddCartCtrl = StreamController<bool>.broadcast();
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  BuyerViewProductBloc(this.context, this.productList,
      // this.productReference,
      this.storeReference, this.isFromAddProduct, this.initialProductIndex, this.openingFrom, this.searchedText,);
  // endregion

  // region Init
  void init()async {
    //Add all products to Product data model
    await addAllProductsToProductDataModel();
    ///Shopping cart quantity data model
    shoppingCartQuantityDataModel = Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);
    //screenRefreshCtrl.sink.add(true);
    storeProductServices = StoreProductServices();
    productCommentServices = ProductCommentServices();
    shoppingCartServices = ShoppingCartServices();
    productAndImageServices = ProductAndImageServices();
    //Initialize buyerViewProductPagination
    buyerViewProductPagination = BuyerViewProductPagination(context,this);
    ///Store
    // singleStoreInfoServices = SingleStoreInfoServices();
    // onChangeProduct(0);
    // getCartDetails();
    //Get single product detail
    //print(productList);

  }
// endregion

  //region Add all products to Product data model
  Future<void> addAllProductsToProductDataModel()async{
    singleProductViewCtrl.sink.add(BuyerViewProductState.Loading);
    //Add offset value
    // offset = productList.length;
    await Future.delayed(const Duration(milliseconds: 100));
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    //Add all products
    productDataModel.addProductIntoList(products: productList);
    //Scroll to the index
    //await scrollToIndex(initialProductIndex);
  }
  //endregion

  //region Listen scroll
  Future<void> scrollToIndex(int index) async{
    //Success
    singleProductViewCtrl.sink.add(BuyerViewProductState.Success);
    await Future.delayed( Duration.zero);
    double screenHeight = MediaQuery.of(context).size.width;

    // Calculate the offset for the given index.
    double offset = index * screenHeight;

    // Use the animateTo method to scroll to the calculated offset
    scrollController.animateTo(
      offset,
      duration: const Duration(seconds: 1),
      curve: Curves.easeInOut,
    );


  }
  //endregion

  //region Create product share link
  createProductShareLink({required String productRef,required String? imageUrl}) async {
    //
    //
    // //If store ia not activated then show message that activate you store to create to share your product
    // if(AppConstants.appData.isStoreView! &&!sellerDashboardDataModel.storeDashBoard!.isActive!){
    //   return CommonMethods.toastMessage(AppStrings.activateYourStoreFirstToStartShareProduct, context);
    // }
    //Open share bottom sheet
    //   onTapShare(link:AppLinkCreateService().createProductLink(productReference: productRef), imageUrl:imageUrl);
  }
  //endregion

  //region Go to store screen
  void goToStore({required String storeReference}){
    var screen = BuyerViewStoreScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Save and un-Save Product
  saveUnSaveProduct(
    products.Product product,
  ) async {
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.saveProduct! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }
    try {
      product.saveStatus = !product.saveStatus!;
      saveCtrl.sink.add(BuyerViewProductState.Success);
      // buyerViewStoreProductCtrl.sink.add(BuyerViewStoreState.Loading);
      await storeProductServices.saveUnSaveProduct(product.productReference!);
      //

    }   on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

    }
  }
  //endregion

  //region Go to Buyer Product Detail Screen
  void goToProductDetail(products.Product selectedProduct) {
    var screen = BuyerProductDetailsScreen(
      isFromAddProduct:isFromAddProduct ,
      // productCategory:selectedProduct.productCategory! ,
      // productDesc:selectedProduct.productDescription! ,
      // productVersion:selectedProduct.productVersion! ,
      // url:selectedProduct.promotionLink!,
      // productReference:selectedProduct.productReference!,
      // updatedDate:selectedProduct.updatedDate!,
      // storeReference:selectedProduct.storeReference!,
      // createdDate: selectedProduct.createdDate!,
      // returnPeriod:selectedProduct.returnPeriod! ,
      // deliveryDays: selectedProduct.deliveryBy,
      // returnReasons: selectedProduct.returnConditions,
      // returnCostPaidBy: selectedProduct.returnCostOn,
      // deliveryPartner: selectedProduct.deliveryPartner,
      // logisticPartnerName: selectedProduct.logisticPartnerName,
      // deliveryFee: selectedProduct.deliveryFee,
      // returnPickupBy: selectedProduct.returnPickupBy,
      product: selectedProduct,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region On tap edit details
  void onTapEditDetails({required String productReference,required int storeId}){
    //Add product reference
    var screen = EditProductDetailsScreen(storeId:storeId, productReferenceList: [productReference],);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {

      //If null then return
      if(value == null){
       return;
      }
      //Else add updated data to the exact object which is coming from edit screen
      else{

        //Take out the index
        int indexToReplace = productList.indexWhere((obj) => obj.productReference == value.postReference);
        // Check if the object to replace was found
        if (indexToReplace != -1) {
          // Replace the object at the specified index with the new object
          productList[indexToReplace] = value;
          //Refresh
          singleProductViewCtrl.sink.add(BuyerViewProductState.Success);
        } else {
          return;
        }
      }




    });
  }
  //endregion

  //region Go to Buyer Image Preview Screen
  void goToBuyerProductImageScreen({required List<products.ProdImages> productImage,required imageIndex}) {
    List<String> imageUrls = [];
    for (var data in productImage) {
      imageUrls.add(data.productImage!);
    }
    var screen = BuyerImagePreviewScreen(
      productImage: imageUrls,
      imageIndex:imageIndex ,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }
  //endregion

  //region On Tap Share
  void onTapShare({required String? imageUrl,required String productReference}) {

    CommonMethods.accessBottomSheet(

      screen: ShareWithImageScreen(
        imageType: CustomImageContainerType.product ,
        entityType: EntityType.PRODUCT,
        objectReference: productReference,

        url:AppLinkCreateService().createProductLink(productReference: productReference),
      imageLink:imageUrl ,
    ), context: context,);
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion

  //region On Tap Comment
  void onTapComment() {
    commentBottomSheetVisibility = !commentBottomSheetVisibility;
    commentBottomSheetCtrl.sink.add(commentBottomSheetVisibility);
  }
  //endregion

  //region View comment
  viewComment({required String productRef}) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewCommentQuestionReview! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }

    var screen = SinglePostViewScreen(postReference: productRef,isFromProductScreen: true,);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
    return ;
    // return context.mounted?CommonMethods.toastMessage(AppStrings.thisFeatureIsCommingSoon, context):null;
    //
    //
    //
    // var screen = BuyerProductCommentScreen(
    //   productRef: productRef,
    //   storeReference:storeReference,
    //   isWriteComment: isWriteComment,
    //
    //
    //
    // );
    // var route = CupertinoPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
  //endregion


  //region Add comment
  addComment({required String productRef, required int productId, required String storeReference,required bool isWriteComment}) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.addComment != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccessViewComment, context);
    // }
    var screen = SinglePostViewScreen(postReference: productRef,isFromProductScreen: true,);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
    return ;
    // return context.mounted?CommonMethods.toastMessage(AppStrings.thisFeatureIsCommingSoon, context):null;
    // //Todo
    // var screen = BuyerProductCommentScreen(
    //   productRef: productRef,
    //   storeReference:storeReference,
    //   isWriteComment: isWriteComment,
    //
    // );
    // var route = CupertinoPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
  //endregion

  //region Share Link
  void shareLinkMessage() async {
    //print("Hello");
    await Share.share("https://swadesic.com/");
  }
  //endregion

  //region On Change Slider
  void onChangeSlider(int index) {
    sliderCtrl.sink.add(index);
    //print(index);
  }
//endregion

  //region Switch to buyer
  void switchToBuyer({required String productReference})async{

    //Switch to buyer
    CommonMethods.switchToBuyer(context: context);
    await Future.delayed(const Duration(seconds: 2));
    //Push to product screen
    var screen = BuyerViewSingleProductScreen(productReference:productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);

  }
  //endregion

  // region Go to Single product screen
  goToSingleProductScreen({required String productReference}) {
    var screen = BuyerViewSingleProductScreen(productReference:productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
//endregion

  //region On tap Buy not / Add to Cart
  void onTapBuyAddCart(
    int storeId,
    int productId,
  ) {
    buyNowAddCartCtrl.sink.add(true);
    CommonMethods.toastMessage("Item has been added to your cart", context);

    ///addToCart(storeId,productId);
    gotoShoppingCart();
  }

  //endregion


  //region On tap drawer
  void onTapDrawer({
    required String productReference,
    required String storeReference,
    required String? productImage,
    required Product product})async{

    List<Map<String, dynamic>> accessOptions = [
      {
        'title': AppStrings.copyProductLink,
        'onTap': () {
          Navigator.pop(context);
          CommonMethods.copyText(context, AppLinkCreateService().createProductLink(productReference: productReference));
        },
      },
      {
        'title': AppStrings.shareTheProduct,
        'onTap': () {
          Navigator.pop(context);
          ProductDetailFullCardBloc(context, product, false).onTapShare(
              imageUrl: productImage,
              productReference: productReference);
        },
      },
      {
        'title': AppStrings.reportTheProduct,
        'onTap': () {
          Navigator.pop(context);
          // Navigator.pop(context);
          var screen = ReportScreen(
            reference: productReference,
            isProduct: true,
          );
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(context, route);

//endregion

        },
      },
      // //Delete
      //
      // {
      //   'title': AppStrings.deleteProduct,
      //   'onTap': () {
      //     askConfirmBeforeDelete(productReference: productReference);
      //
      //   },
      // },
      // Add more options if needed
    ];

    // Add delete product option only if the user is the owner of the store
    if (AppConstants.appData.isStoreView! &&
        (storeReference == AppConstants.appData.storeReference!)) {
      accessOptions.add({
        'title': AppStrings.deleteProduct,
        'onTap': () {
          askConfirmBeforeDelete(productReference: productReference);
        },
      });
    }

    CommonMethods.accessBottomSheet(screen: ShareAccessBottomSheet(accessOptions: accessOptions), context: context,);

    // //If store ia not activated then show message that activate you store to create to share your product
    // if(AppConstants.appData.isStoreView! &&!sellerDashboardDataModel.storeDashBoard!.isActive!){
    //   return CommonMethods.toastMessage(AppStrings.activateYourStoreFirstToStartShareProduct, context);
    // }
    // //Open share bottom sheet
    // onTapShare(link:AppLinkCreateService().createProductLink(productReference: productReference), imageUrl:productImage);
    //
    //
  }
  //endregion

  //endregion

  //region Get Product Image
  //   getProductImage(int productId)async{
  //     try{
  //       buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Loading);
  //       productImageResponse =await productAndImageServices.getOnlyProductImage(productId);
  //       ///Check saves or not
  //       saveUnSaveStatus(productId);
  //       buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Success);
  //     }
  //     on ApiErrorResponseMessage catch(error){
  //       buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Failed);
  //
  //       return;
  //     }
  //     catch(error){
  //       buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Failed);
  //
  //       return;
  //     }
  //   }
  //endregion

  //region Add to cart

  // region addToCart(int storeId,int productId,)async{
  addToCart({required int storeId, required String productReference, bool goToCart = false, required String storeRference}) async {
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.buyProduct! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }

    ///Check is the store reference is contains in store list created by user.
    ///If not then return "You have to purchases from own store."
    //If empty store list
    if(BuyerHomeBloc.storeListResponse.storeList!.isEmpty){
      return CommonMethods.toastMessage(AppStrings.youCanOnlyOrderFromOtherStoreAfterOfficialLaunch, context,toastShowTimer:10);
    }
    //If store reference does not contains in list
    if(BuyerHomeBloc.storeListResponse.storeList!.where((element) => element.storeReference! == storeRference).isEmpty){
      return CommonMethods.toastMessage(AppStrings.youCanOnlyOrderFromOtherStoreAfterOfficialLaunch, context,toastShowTimer: 10);
    }


    try {
      addToCartResponse = await shoppingCartServices.addToCart(productReference: productReference, storeId: storeId);
      ///Add product reference to the data model
      shoppingCartQuantityDataModel.updateCartQuantity(productReference:productReference);


      // AppConstants.cartItemIdList.add(addToCartResponse.data!.cartitemid!);
      // getCartDetails();
      // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
      //
      // //print(AppConstants.cartItemIdList);
      CommonMethods.toastMessage('Product has been added to your cart', context);
      screenRefreshCtrl.sink.add(true);
      if (goToCart) {
        gotoShoppingCart();
      }
      return;

      //Refresh ui

      // buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Success);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }
//endregion

//region Go to Shopping Cart Screen
  gotoShoppingCart() {
    // AppConstants.userLevelPersistentTabController.jumpToTab(2);
    // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);

    // AppConstants.cartItemIdList.clear();
    var screen = const ShoppingCartScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value){
      screenRefreshCtrl.sink.add(true);
    }).then((value){
    });
  }
//endregion



  ///region Pickup pin code  dialog
  // void pickupPinCode({required int storeId, required String productReference, bool goToCart = false}) {
  //   ///Access check
  //   if (BuyerHomeBloc.userDetailsResponse.userDetail!.buyProduct! != "1") {
  //    CommonMethods.toastMessage(AppStrings.noAccess, context);
  //    return;
  //   }
  //   showDialog(
  //       context: context,
  //       builder: (BuildContext context) {
  //         return AlertDialog(
  //           title:  Center(child: Text(AppStrings.enterPickupPinCode)),
  //           titleTextStyle: const TextStyle(color: AppColors.writingColor2, fontSize: 16, fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600),
  //           content:PickupPinCode(
  //               onTapDone: addToCart(storeId:storeId,
  //           productReference: productReference,
  //             goToCart: goToCart
  //           )),
  //         );
  //       });
  // }
//endregion


//region Dispose
  void dispose() {
    imageCache.clear();
    sliderCtrl.close();
    singleProductViewCtrl.close();
    shareBottomSheetCtrl.close();
    commentBottomSheetCtrl.close();
    // AppConstants.cartItemIdList.clear();
  }
//endregion

  ///Single product api call
//region Single product Api Call
  ///Not in use
//region On Change Product
// void onChangeProduct(int index)async{
//   //print(productIdList);
//    productId = productIdList[index];
//
//   //print(productId);
//   ///Get Product Detail
//   getProductDetail(productId);
//
//   ///Get Product Image
//   getProductImage(productId);
//   ///Get Product comment
//   getProductAllCommentApiCall(productId);
//
//
// }
//endregion


  //region Open update stocks
  void onTapUpdateStock({required Product product}) async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topRight: Radius.circular(20),topLeft: Radius.circular(20)),
        ),
        builder: (context) {
          return SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Text(
                      AppStrings.updateStock,
                      style: AppTextStyle.sectionSemiBold(textColor: AppColors.appBlack),
                    ),
                  ),
                  UpdateStock(product: product,),
                ],
              ));
        }).then((value) {
    });
  }
//endregion


  //region On tap heart
  Future<void>onTapHeart({required Product product,bool isDoubleTapped = false})async{
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try{

      //If already liked and double tapped then return
      if(product.likeStatus! && isDoubleTapped){
        return;
      }
      //Update like status in product data model
      productDataModel.likeStatusUpdate(productReference: product.productReference!, likeStatus: !product.likeStatus!);



      screenRefreshCtrl.sink.add(true);

      //Api call
      bool status = await PostService().likePost(postReference: product.productReference!, likeStatus: isDoubleTapped?true:product.likeStatus!);



    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion



  //region Go to liked user or store
  void goToLikedUsedOrStoreScreen({required String reference}){
    var screen = LikedUserOrStoreScreen(contentReference: reference);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion


//region Ask confirmation before delete
void askConfirmBeforeDelete({required String productReference}) {
  //Get reference to Product data model
  var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
  CommonMethods.appDialogBox(

      context: context,
      widget: SaveOrDiscard(
        message: AppStrings.areYouSureWantsToDelete,
        firstButtonName: "Delete",
        isMessageVisible: true,
        popPreviousScreen: true,

        onTapSave:(v){
          //Delete product
          SellerHideDeleteService().deleteProduct(productReference);
          //remove product from product dat model
          productDataModel.allProducts.removeWhere((element) => element.productReference == productReference);
          productList.removeWhere((element) => element.productReference == productReference);
          //Update ui
          productDataModel.updateUi();
          //Pop bottom sheet
          Navigator.pop(context);
          // requestForSaveVerificationType();
        },previousScreenContext: context,)
  );
}
//endregion


}
