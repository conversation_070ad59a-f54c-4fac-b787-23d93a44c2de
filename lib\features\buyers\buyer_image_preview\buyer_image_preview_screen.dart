import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Buyer Image preview
class BuyerImagePreviewScreen extends StatefulWidget {
  final List<String> productImage;
  final int imageIndex;

  const BuyerImagePreviewScreen({Key? key, required this.productImage, this.imageIndex = 0}) : super(key: key);

  @override
  _BuyerImagePreviewScreenState createState() => _BuyerImagePreviewScreenState();
}
// endregion

class _BuyerImagePreviewScreenState extends State<BuyerImagePreviewScreen> {
  // region Buyer Image Preview Bloc
  late BuyerImagePreviewBloc buyerImagePreviewBloc;

  // endregion

  // region Init
  @override
  void initState() {
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.top]);
    //print(widget.productImage.length);
    buyerImagePreviewBloc = BuyerImagePreviewBloc(context, widget.imageIndex);
    buyerImagePreviewBloc.init();
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    PaintingBinding.instance.imageCache.clear();
    imageCache.clear();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appBlack,
      // appBar: appBar(),
      resizeToAvoidBottomInset: false,
      body: body(),
      // body: SafeArea(
      //     child: body()
      //     child:ListView.builder(
      //         shrinkWrap: true,
      //         //controller: buyerViewProductBloc.listViewPageCtrl,
      //         scrollDirection: Axis.vertical,
      //         itemCount: 50,
      //         itemBuilder:(BuildContext,index){
      //           return Container(height: 100,width: 100,color: index%2 == 0 ?Colors.red:Colors.green);
      //         }
      //
      //     ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        backgroundColor: Colors.transparent,
        leadingIconColor: AppColors.appWhite,
        context: context,
        isCustomTitle: false,
        title: "",
        isCartVisible: false,
        isMembershipVisible: false,
        isDefaultMenuVisible: false);
  }

  //endregion

  // region Body
  Widget body() {
    return Stack(
      children: [
        productImage(),
        Positioned(
          left: 0,
          right: 0,
          top: 0,
          child: StreamBuilder<bool>(
              stream: buyerImagePreviewBloc.hideAndVisibleAppBarCtrl.stream,
              initialData: false,
              builder: (context, snapshot) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: snapshot.data! ? kToolbarHeight + 30 : 0,
                  child: appBar(),
                );
              }),
        ),
        Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [dotes(), smallImage()],
            ))
      ],
    );
  }

// endregion

//region Product images
  Widget productImage() {
    return Container(
      alignment: Alignment.bottomCenter,
      width: MediaQuery.of(context).size.width,
      child: StreamBuilder<bool>(
          stream: buyerImagePreviewBloc.refreshCtrl.stream,
          initialData: buyerImagePreviewBloc.zoomStatus,
          builder: (context, snapshot) {
            return PageView.builder(
                controller: buyerImagePreviewBloc.pageController,
                // allowImplicitScrolling: buyerImagePreviewBloc.zoomStatus,
                // physics: NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  buyerImagePreviewBloc.onChangeSlider(index);
                  //buyerImagePreviewBloc.onDoubleTap();
                },
                itemCount: widget.productImage.length,
                // physics: AlwaysScrollableScrollPhysics(),
                physics: buyerImagePreviewBloc.zoomStatus ? const NeverScrollableScrollPhysics() : const AlwaysScrollableScrollPhysics(),

                ///controller: buyerViewProductBloc.imageSliderPageCtrl,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      buyerImagePreviewBloc.onTapImage();
                    },
                    child: extendedImage(widget.productImage[index], context, 1000, 1000, fit: BoxFit.fitWidth,isFromImagePreview: true),
                  );
                });
            // return PhotoViewGallery.builder(
            //   scrollPhysics: const BouncingScrollPhysics(),
            //   builder: (BuildContext context, int index) {
            //     return PhotoViewGalleryPageOptions(
            //
            //       errorBuilder: (context, object, stackTrace){
            //         return SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.cover,);
            //       },
            //       // minScale: 0.4,
            //       controller: buyerImagePreviewBloc.photoViewController,
            //       imageProvider: NetworkImage("${AppConstants.baseUrl}/${widget.productImage[index]}"),
            //       // initialScale:  0.0,
            //
            //       // heroAttributes: PhotoViewHeroAttributes(tag: galleryItems[index].id),
            //     );
            //   },
            //    customSize: Size(MediaQuery.of(context).size.width,MediaQuery.of(context).size.height),
            //   itemCount: widget.productImage.length,
            //   loadingBuilder: (context, event){
            //     return SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.cover,);
            //   },
            //   backgroundDecoration:const BoxDecoration(color: AppColors.appWhite),
            //   pageController: buyerImagePreviewBloc.pageController,
            //   onPageChanged: (index){
            //     buyerImagePreviewBloc.onChangeSlider(index);
            //   },
            // );
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////

            // return PageView.builder(
            //   controller: buyerImagePreviewBloc.pageController,
            //     // allowImplicitScrolling: buyerImagePreviewBloc.zoomStatus,
            //     // physics: NeverScrollableScrollPhysics(),
            //     onPageChanged: (index){
            //       buyerImagePreviewBloc.onChangeSlider(index);
            //       //buyerImagePreviewBloc.onDoubleTap();
            //     },
            //     itemCount: widget.productImage.length,
            //     // physics: AlwaysScrollableScrollPhysics(),
            //     physics: buyerImagePreviewBloc.zoomStatus?const NeverScrollableScrollPhysics():const AlwaysScrollableScrollPhysics(),
            //
            //     ///controller: buyerViewProductBloc.imageSliderPageCtrl,
            //     itemBuilder: (context, index){
            //     return PhotoView(
            //       controller:buyerImagePreviewBloc.photoViewController ,
            //
            //
            //       backgroundDecoration:const BoxDecoration(
            //         color: AppColors.appWhite
            //       ) ,
            //
            //       enableRotation: false,
            //       errorBuilder: (context, object, s){
            //         return SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.cover,);
            //       },
            //       loadingBuilder: (context,event){
            //         return SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.cover,);
            //       },
            //       imageProvider:NetworkImage("${AppConstants.baseUrl}/${widget.productImage[index]}"),);
            //     }
            //
            // );
          }),
    );
  }

//endregion

//region Dots
  Widget dotes() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      height: 6,
      child: StreamBuilder<int>(
          stream: buyerImagePreviewBloc.sliderCtrl.stream,
          // stream: buyerImagePreviewBloc.sliderCtrl.stream,
          initialData: 0,
          builder: (context, snapshot) {
            return ListView.builder(
                itemCount: widget.productImage.length,
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemBuilder: (Context, Index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: SvgPicture.asset(
                      AppImages.dot,
                      height: 5.29,
                      width: 5.29,
                      color: snapshot.data == Index ? AppColors.darkGray : AppColors.darkStroke,
                    ),
                  );
                });
          }),
    );
  }

//endregion

//region Small image
  Widget smallImage() {
    // StreamBuilder<bool>(
    //     stream: buyerImagePreviewBloc.hideAndVisibleAppBarCtrl.stream,
    //     initialData: false,
    //     builder: (context, snapshot) {
    //       return AnimatedContainer(
    //         duration: Duration(milliseconds: 300),
    //         height: snapshot.data! ? kToolbarHeight : 0,
    //         child: Positioned(
    //             left: 0,
    //             right: 0,
    //             top: 0,
    //             child: appBar()),
    //       );
    //     }
    // )

    return StreamBuilder<bool>(
        stream: buyerImagePreviewBloc.hideAndVisibleAppBarCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          return SafeArea(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: snapshot.data! ? MediaQuery.of(context).size.height / 10 : 0,
              child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: widget.productImage.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        buyerImagePreviewBloc.onSelectSmallImage(index);
                      },
                      child: SizedBox(
                          width: MediaQuery.of(context).size.height / 10,
                          child: extendedImage(widget.productImage[index], context, 200, 200, cache: true, fit: BoxFit.cover,customPlaceHolder: AppImages.productPlaceHolder)),
                    );
                  }),
            ),
          );
        });
  }
//endregion
}
