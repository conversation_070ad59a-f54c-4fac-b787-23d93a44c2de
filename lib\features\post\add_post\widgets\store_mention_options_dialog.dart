import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class StoreMentionOptionsDialog extends StatelessWidget {
  final SuggestionItem storeItem;
  final Function(SuggestionItem, bool) onOptionSelected;

  const StoreMentionOptionsDialog({
    super.key,
    required this.storeItem,
    required this.onOptionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              'Mention Store',
              style: AppTextStyle.contentHeading0(
                textColor: AppColors.appBlack,
              ),
            ),
            const SizedBox(height: 16),

            // Store info
            Row(
              children: [
                CustomImageContainer(
                  width: 42,
                  height: 42,
                  imageUrl: storeItem.imageUrl,
                  imageType: CustomImageContainerType.store,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        storeItem.primaryText ?? '',
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (storeItem.secondaryText != null)
                        Text(
                          storeItem.secondaryText!,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack1,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Options
            _buildOption(
              context,
              'Mention Store Only',
              'Mention the store directly',
              Icons.store,
              () => onOptionSelected(storeItem, false),
            ),
            const SizedBox(height: 12),
            _buildOption(
              context,
              'Mention Store Products',
              'Choose a specific product from this store',
              Icons.inventory,
              () => onOptionSelected(storeItem, true),
            ),
            const SizedBox(height: 20),

            // Cancel button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Cancel',
                  style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack1,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        onTap();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.textFieldFill0),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.brandBlack,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.writingBlack1,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
